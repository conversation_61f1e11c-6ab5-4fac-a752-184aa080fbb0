#!/usr/bin/env python3
"""
Тест для проверки функциональности сокращения текста в cerebras_client.py
"""

import sys
import os

# Добавляем текущую директорию в путь для импорта
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cerebras_client import summarize_text, remove_think_tags

def test_remove_think_tags():
    """Тест функции удаления тегов </think>"""
    print("=== Тест удаления тегов </think> ===")
    
    # Тест 1: Текст с тегами
    text_with_tags = "Это обычный текст <think>размышления</think> и еще текст </think> конец"
    result = remove_think_tags(text_with_tags)
    print(f"Исходный текст: {text_with_tags}")
    print(f"Результат: {result}")
    print(f"Теги удалены: {'✓' if '<think>' not in result and '</think>' not in result else '✗'}")
    print()
    
    # Тест 2: Текст без тегов
    text_without_tags = "Обычный текст без тегов"
    result2 = remove_think_tags(text_without_tags)
    print(f"Текст без тегов: {text_without_tags}")
    print(f"Результат: {result2}")
    print(f"Текст не изменился: {'✓' if result2 == text_without_tags else '✗'}")
    print()

def test_summarize_text():
    """Тест функции сокращения текста"""
    print("=== Тест сокращения текста ===")
    
    # Длинный тестовый текст (больше 600 символов)
    long_text = """
    Искусственный интеллект (ИИ) — это область компьютерных наук, которая занимается созданием интеллектуальных машин, способных работать и реагировать как люди. ИИ включает в себя множество подобластей, таких как машинное обучение, обработка естественного языка, компьютерное зрение и робототехника. 
    
    Машинное обучение является подмножеством ИИ, которое позволяет компьютерам учиться и принимать решения на основе данных без явного программирования для каждой конкретной задачи. Существует несколько типов машинного обучения: обучение с учителем, обучение без учителя и обучение с подкреплением.
    
    Глубокое обучение — это подмножество машинного обучения, которое использует искусственные нейронные сети с множеством слоев для анализа различных факторов данных. Эта технология особенно эффективна в задачах распознавания изображений, обработки речи и естественного языка.
    
    Применения ИИ в современном мире очень разнообразны: от рекомендательных систем в социальных сетях до автономных автомобилей, от медицинской диагностики до финансового анализа. ИИ революционизирует многие отрасли и меняет способы нашей работы и жизни.
    """
    
    print(f"Длина исходного текста: {len(long_text)} символов")
    print("Исходный текст:")
    print(long_text[:200] + "..." if len(long_text) > 200 else long_text)
    print()
    
    print("Отправляем запрос на сокращение...")
    result = summarize_text(long_text.strip())
    
    if result:
        print("✓ Сокращение выполнено успешно!")
        print(f"Длина сокращенного текста: {len(result)} символов")
        print(f"Степень сокращения: {(1 - len(result)/len(long_text.strip()))*100:.1f}%")
        print()
        print("Сокращенный текст:")
        print(result)
        print()
        
        # Проверяем, что теги </think> удалены
        has_think_tags = '<think>' in result or '</think>' in result
        print(f"Теги </think> отсутствуют: {'✓' if not has_think_tags else '✗'}")
        
    else:
        print("✗ Ошибка при сокращении текста")
        print("Возможные причины:")
        print("- Проблемы с API ключами")
        print("- Сетевые проблемы")
        print("- Превышение лимитов API")

def main():
    """Основная функция тестирования"""
    print("ТЕСТИРОВАНИЕ МОДУЛЯ CEREBRAS_CLIENT.PY")
    print("=" * 50)
    print()
    
    # Тест 1: Удаление тегов
    test_remove_think_tags()
    
    # Тест 2: Сокращение текста
    test_summarize_text()
    
    print("=" * 50)
    print("ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")

if __name__ == "__main__":
    main()
