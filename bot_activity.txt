2025-07-23 00:28:32 - INFO - Rate limiter cleanup completed. Active users: 8 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 0 private podcasts, 0 AI responses, 0 summaries
2025-07-23 00:28:32 - INFO - [GenAI] Starting connection warmup for 1 keys
2025-07-23 00:28:32 - INFO - [GenAI] Connection warmup completed: 1/1 successful
2025-07-23 00:28:32 - INFO - [GenAI] All key states reset to normal
2025-07-23 00:33:20 - INFO - [ResourceManager] Cleaned up 1 expired clients
2025-07-23 01:08:33 - INFO - Rate limiter cleanup completed. Active users: 4 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 0 private podcasts, 0 AI responses, 0 summaries
2025-07-23 01:08:33 - INFO - Force cleanup: removed 0 completed futures
2025-07-23 01:28:34 - INFO - [GenAI] Starting connection warmup for 1 keys
2025-07-23 01:28:34 - INFO - [GenAI] Connection warmup completed: 1/1 successful
2025-07-23 01:28:34 - WARNING - Too many active futures (21), forcing cleanup and GC
2025-07-23 01:33:20 - INFO - [ResourceManager] Cleaned up 1 expired clients
2025-07-23 03:25:53 - INFO - user 7912629458 (.) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29185 in private chat
2025-07-23 03:27:39 - INFO - Настройки пользователей сохранены в /home/<USER>/Бот Подкаст/bot_data.json
2025-07-23 03:27:44 - INFO - user 7912629458 (.) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29189 in private chat
2025-07-23 07:18:24 - INFO - Database initialized successfully
2025-07-23 07:18:24 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-23 07:18:24 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-23 07:18:24 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-23 07:18:24 - INFO - Loaded bot data: 2 admins, 26 blocked users, 1 scheduled podcasts, 1 pro users, VEO enabled: True
2025-07-23 07:18:24 - INFO - Loaded 0 group model settings
2025-07-23 07:18:24 - INFO - ThreadPoolManager initialized with max_workers=16
2025-07-23 07:18:24 - INFO - Initialized podcast queue with 50 workers
2025-07-23 07:18:24 - INFO - HTML Group: Module initialized
2025-07-23 07:18:24 - INFO - HTML Group: Module ready for use!
2025-07-23 07:18:24 - WARNING - 🚀 Запуск бота через start_bot()...
2025-07-23 07:18:24 - INFO - Log file is expected at: /home/<USER>/Бот Подкаст/bot_activity.txt
2025-07-23 07:18:24 - INFO - Checking for ffmpeg...
2025-07-23 07:18:24 - INFO - ffmpeg found.
2025-07-23 07:18:24 - INFO - Initializing telegra.ph client...
2025-07-23 07:18:24 - INFO - loaded telegra.ph token from .telegraph_token
2025-07-23 07:18:24 - INFO - telegra.ph client initialized for account: UseShBot
2025-07-23 07:18:24 - INFO - telegra.ph client ready.
2025-07-23 07:18:24 - INFO - Загрузка настроек пользователей...
2025-07-23 07:18:24 - INFO - Настройки пользователей загружены (67 пользователей)
2025-07-23 07:18:24 - INFO - Настройки пользователей успешно загружены.
2025-07-23 07:18:24 - INFO - Инициализация базы данных...
2025-07-23 07:18:24 - INFO - Database initialized successfully
2025-07-23 07:18:24 - INFO - База данных успешно инициализирована.
2025-07-23 07:18:24 - INFO - Unified background scheduler started
2025-07-23 07:18:24 - INFO - Unified background scheduler thread started (оптимизированный).
2025-07-23 07:18:24 - INFO - Cleanup scheduler disabled by configuration (AUTO_CLEANUP_ENABLED=False)
2025-07-23 07:18:24 - INFO - Bot sh is starting...
2025-07-23 07:18:24 - INFO - MAIN: Bot polling starting...
2025-07-23 07:18:29 - INFO - 🔥 Запуск разогрева соединений для быстрого отклика...
2025-07-23 07:18:29 - INFO - [GenAI] Starting connection warmup for 2 keys
2025-07-23 07:18:30 - INFO - [GenAI] Connection warmup completed: 2/2 successful
2025-07-23 07:18:30 - INFO - ✅ Разогрев завершен: 2 соединений готовы
2025-07-23 07:21:30 - INFO - ThreadPoolExecutor created with 16 workers
2025-07-23 07:21:30 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: шаверма продажа магазин от чурки всё написано еле еле на русском...
2025-07-23 07:22:08 - INFO - HTML Group: Request from @se7entomo in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: симулятор побега от коллектора, визуальная новелла с элементами рпг, главный герой - компания гугл и...
2025-07-23 07:22:27 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:22:42 - INFO - HTML Group: Successfully generated and sent HTML file 'шаверма_продажа_магазин.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:22:48 - INFO - HTML Group: Request from @se7entomo in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: симулятор побега от коллектора, визуальная новелла с элементами рпг, главный герой - компания гугл и...
2025-07-23 07:23:24 - INFO - Rate limiter cleanup completed. Active users: 1 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 0 private podcasts, 0 AI responses, 0 summaries
2025-07-23 07:23:24 - INFO - Force cleanup: removed 0 completed futures
2025-07-23 07:23:25 - INFO - Scheduled podcast checker started
2025-07-23 07:23:47 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: симулятор минета, типа 3д анимация как тебе делают минет мега реализм детали...
2025-07-23 07:24:15 - INFO - HTML Group: Request from @se7entomo in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: симулятор ледибаг но тики убийца...
2025-07-23 07:24:16 - INFO - HTML Group: Successfully generated and sent HTML file 'симулятор_побега_от.html' for @se7entomo using Gemini 2.5 Pro
2025-07-23 07:24:18 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:24:50 - INFO - HTML Group: Successfully generated and sent HTML file 'симулятор_минета_типа.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:25:09 - INFO - user 1063347719 (@se7entomo) (se7entomoinoue 🫧) - Ignoring reply to HTML file 491961
2025-07-23 07:26:06 - INFO - HTML Group: Request from @se7entomo in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: симулятор побега от коллектора, визуальная новелла с элементами рпг, главный герой - компания гугл и...
2025-07-23 07:26:31 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: 3d жопа...
2025-07-23 07:26:46 - INFO - Sent Diana approval notification to admin ********** for user 414169591
2025-07-23 07:26:46 - INFO - Sent Diana approval notification to admin ********** for user 414169591
2025-07-23 07:26:46 - INFO - Notified admins about new Diana & Sasha podcast request from user 414169591
2025-07-23 07:26:46 - INFO - New user 414169591 detected in private chat - admins notified for Diana & Sasha approval
2025-07-23 07:26:52 - INFO - Admin ********** approved user 414169591 for Diana & Sasha podcast
2025-07-23 07:27:14 - INFO - Admin ********** unblocked user 414169591
2025-07-23 07:27:21 - INFO - HTML Group: Successfully generated and sent HTML file '3d_жопа.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:27:33 - INFO - HTML Group: Successfully generated and sent HTML file 'симулятор_побега_от.html' for @se7entomo using Gemini 2.5 Pro
2025-07-23 07:31:09 - ERROR - HTML Group: Error in async handler: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-23 07:31:11 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: 1 в 1 приложение сбербанка, но там дохуя денег...
2025-07-23 07:31:49 - INFO - HTML Group: Request from @Qwesad1246 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: ретро форум в стиле y2k где обсуждают как кастомизировать вин 98 в аниме стиле, длинное обсуждение...
2025-07-23 07:31:52 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:32:02 - WARNING - HTML Group: Failed to auto-delete message 491975: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 07:32:15 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: ретро форум в стиле y2k где обсуждают как кастомизировать вин 98 в аниме стиле, длинное обсуждение...
2025-07-23 07:32:25 - INFO - HTML Group: Successfully generated and sent HTML file '1_в_1.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:32:54 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт Apple...
2025-07-23 07:33:11 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: страничка сайта Galaxy слитая где презентация Galaxy S26 Ultra...
2025-07-23 07:33:52 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: афиша концерта моргенштерна в сыктывкаре 2026 с дикими ценами на билете + очень агрессивная подача и...
2025-07-23 07:33:54 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:33:55 - INFO - HTML Group: Successfully generated and sent HTML file 'ретро_форум_в.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:34:00 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: афиша концерта моргенштерна в сыктывкаре 2026 с дикими ценами на билете + очень агрессивная подача и...
2025-07-23 07:34:13 - INFO - user ********** (@kirillshsh) (kirill) - Ignoring reply to HTML file 491985
2025-07-23 07:34:31 - INFO - HTML Group: Successfully generated and sent HTML file 'страничка_сайта_galaxy.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:34:38 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_apple.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:34:52 - INFO - HTML Group: Successfully generated and sent HTML file 'афиша_концерта_моргенштерна.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:35:55 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: афиша концерта моргенштерна в сыктывкаре 2026 но сайт будто делал какой-то колхозник с деревни и лек...
2025-07-23 07:36:44 - INFO - HTML Group: Successfully generated and sent HTML file 'афиша_концерта_моргенштерна.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:37:02 - INFO - user 5090434457 (@Qwesad1246) (Qwe) - Ignoring reply to HTML file 491985
2025-07-23 07:37:56 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: афиша концерта инстасамки в сыктывкаре 2026 но сайт будто делал какой-то колхозник с деревни и лекси...
2025-07-23 07:37:59 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:38:04 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: афиша концерта инстасамки в сыктывкаре 2026 но сайт будто делал какой-то колхозник с деревни и лекси...
2025-07-23 07:38:18 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: ютуб шортс но там одни сиськи и порно...
2025-07-23 07:38:42 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт у которого единственная цель: возбудить парня который зайдёт на него...
2025-07-23 07:38:44 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:38:47 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт у которого единственная цель: возбудить парня который зайдёт на него...
2025-07-23 07:39:14 - INFO - HTML Group: Successfully generated and sent HTML file 'афиша_концерта_инстасамки.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:39:17 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: симулятор рейва на концерте в 3D...
2025-07-23 07:39:27 - INFO - HTML Group: Successfully generated and sent HTML file 'ютуб_шортс_но.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:39:30 - INFO - HTML Group: Request from ID:7994657045 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Eskimo Callboy...
2025-07-23 07:39:41 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_у_которого.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:40:14 - INFO - HTML Group: Successfully generated and sent HTML file 'симулятор_рейва_на.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:40:23 - INFO - HTML Group: Request from @Qwesad1246 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: линукс.ком но его взломали нахуй и сделали про виндовс...
2025-07-23 07:41:07 - INFO - HTML Group: Successfully generated and sent HTML file 'eskimo_callboy.html' for ID:7994657045 using Gemini 2.5 Pro
2025-07-23 07:41:35 - INFO - HTML Group: Successfully generated and sent HTML file 'линукс_ком_но.html' for @Qwesad1246 using Gemini 2.5 Pro
2025-07-23 07:41:48 - INFO - HTML Group: Request from @cactus_asot in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт где можно посмотреть костюмы горничной...
2025-07-23 07:42:02 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт по продаже амфетамина для детей с конченными дозировками и в дизайне наркошопа, белый фон, черн...
2025-07-23 07:42:28 - INFO - HTML Group: Request from ID:7994657045 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Eskimo Callboy 2012...
2025-07-23 07:42:46 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт который убедит парня перестать быть фембоем...
2025-07-23 07:42:55 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_где_можно.html' for @cactus_asot using Gemini 2.5 Pro
2025-07-23 07:43:00 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_по_продаже.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:43:25 - INFO - Scheduled podcast checker started
2025-07-23 07:43:27 - INFO - HTML Group: Successfully generated and sent HTML file 'eskimo_callboy_2012.html' for ID:7994657045 using Gemini 2.5 Pro
2025-07-23 07:43:54 - INFO - HTML Group: Request from @cactus_asot in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт который убедит меня стать фембоем (опять)...
2025-07-23 07:43:59 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_который_убедит.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:44:31 - INFO - user ********** (@kirillshsh) (kirill) - Ignoring reply to HTML file 492033
2025-07-23 07:44:55 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт с 1 в 1 дизайном RAMP 2015 с тем же назначением...
2025-07-23 07:44:55 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_который_убедит.html' for @cactus_asot using Gemini 2.5 Pro
2025-07-23 07:45:13 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:45:24 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: сайт с 1 в 1 дизайном RAMP 2015 с тем же назначением...
2025-07-23 07:45:47 - INFO - HTML Group: Request from ID:7994657045 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Сто восемь.О музыке.
Те ,кто долго жил среди пидарасов,говорят,что они втайне стыдятся своего греха ...
2025-07-23 07:45:50 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: форум наркоманов где никто не собирается слезать и дико торчат, ветка под названием «амфетамин»...
2025-07-23 07:46:00 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:46:58 - INFO - HTML Group: Successfully generated and sent HTML file 'сайт_с_1.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:47:06 - INFO - HTML Group: Successfully generated and sent HTML file 'форум_наркоманов_где.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:47:16 - INFO - HTML Group: Request from ID:7994657045 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Сто восемь.О музыке.
Те ,кто долго жил среди пидарасов,говорят,что они втайне стыдятся своего греха ...
2025-07-23 07:47:19 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 07:51:15 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 07:51:15 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 07:51:15 - INFO - Notified admins about new Diana & Sasha podcast request from user **********
2025-07-23 07:51:15 - INFO - New user ********** detected in private chat - admins notified for Diana & Sasha approval
2025-07-23 07:51:15 - INFO - user ********** (@firetimetiktok) (αх!⁶⁷) - Reaction will be set after model determination in processing_core.
2025-07-23 07:51:15 - INFO - user ********** - processing request buffer with 1 items.
2025-07-23 07:51:15 - INFO - user ********** - combined buffer: text length=5, num_images=0, reply_to=29213
2025-07-23 07:51:15 - INFO - Recorded AI response request for user **********
2025-07-23 07:51:15 - INFO - user ********** - Reaction will be set after model determination
2025-07-23 07:51:15 - INFO - user ********** - Private chat detected. Using main prompt with user info and individual context (context_key: **********)
2025-07-23 07:51:15 - INFO - user ********** - Started new typing manager
2025-07-23 07:51:15 - INFO - user ********** - Retrieved conversation history with 0 messages for context_key: **********
2025-07-23 07:51:15 - INFO - user ********** - No model override. Defaulting to primary: gemini-2.5-pro.
2025-07-23 07:51:16 - INFO - user ********** - Query classified as LITE, using Gemini 2.5 Flash Lite
2025-07-23 07:51:16 - INFO - user ********** - Attempting regular API call with gemini-2.5-flash for group chat...
2025-07-23 07:51:16 - INFO - Router: model_name='gemini-2.5-flash', call_type='general_primary', is_private_chat=False
2025-07-23 07:51:16 - INFO - Routing to gemini-2.5-flash for group chat: call_type=general_primary
2025-07-23 07:51:19 - WARNING - user ********** - AI response mentions images but no valid image_gen tag found. Response: Привет, αх!⁶⁷! Меня зовут sh, и я твой личный ИИ-помощник. Сегодня 23.07.2025, 10:51 по МСК, и я готов помочь тебе с любыми задачами!

Я здесь, чтобы быть твоим надежным спутником. Вот что я могу:

*   <b>Отвечать на любые вопросы:</b> От простых фактов до сложных концепций, я постараюсь дать тебе с...
2025-07-23 07:51:19 - INFO - user ********** - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit).
2025-07-23 07:51:19 - INFO - Utils send_long_message (chat **********): Message not long (1422 chars). Sending as single message.
2025-07-23 07:51:19 - INFO - Utils send_long_message (chat **********): Sent single message as new message 29216.
2025-07-23 07:51:19 - INFO - user ********** - send_long_message sent final response. Bot message ID: 29216.
2025-07-23 07:51:19 - INFO - Utils remove_reaction (chat **********, msg 29213): Successfully sent request to remove reactions.
2025-07-23 07:51:19 - INFO - user ********** - Response >1000 chars. Removed reactions from user message 29213.
2025-07-23 07:51:19 - INFO - _prepare_summarize_button_if_needed: button creation disabled (chat_id: **********)
2025-07-23 07:51:19 - INFO - user ********** - final_reply_markup is None.
2025-07-23 07:51:20 - INFO - Admin ********** approved user ********** for Diana & Sasha podcast
2025-07-23 07:53:05 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: павел дуров интервью с путиным на сайте
очень агрессивный диалог, особенно путин пиздец злой на дуро...
2025-07-23 07:53:11 - INFO - user ********** - /sh command processed in group -1001508403386
2025-07-23 07:53:30 - INFO - user 7791870156 - /sh command processed in group -1001508403386
2025-07-23 07:53:35 - ERROR - HTML Group: Error processing request: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-23 07:53:40 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: павел дуров интервью с путиным на сайте
очень агрессивный диалог, особенно путин пиздец злой на дуро...
2025-07-23 07:53:54 - INFO - Database initialized successfully
2025-07-23 07:53:54 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-23 07:53:54 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-23 07:53:54 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-23 07:53:54 - INFO - Loaded bot data: 2 admins, 25 blocked users, 1 scheduled podcasts, 1 pro users, VEO enabled: True
2025-07-23 07:53:54 - INFO - Loaded 0 group model settings
2025-07-23 07:53:54 - INFO - ThreadPoolManager initialized with max_workers=16
2025-07-23 07:53:54 - INFO - Initialized podcast queue with 50 workers
2025-07-23 07:53:54 - INFO - HTML Group: Module initialized
2025-07-23 07:53:54 - INFO - HTML Group: Module ready for use!
2025-07-23 07:53:54 - WARNING - 🚀 Запуск бота через start_bot()...
2025-07-23 07:53:54 - INFO - Log file is expected at: /home/<USER>/Бот Подкаст/bot_activity.txt
2025-07-23 07:53:54 - INFO - Checking for ffmpeg...
2025-07-23 07:53:54 - INFO - ffmpeg found.
2025-07-23 07:53:54 - INFO - Initializing telegra.ph client...
2025-07-23 07:53:54 - INFO - loaded telegra.ph token from .telegraph_token
2025-07-23 07:53:54 - INFO - telegra.ph client initialized for account: UseShBot
2025-07-23 07:53:54 - INFO - telegra.ph client ready.
2025-07-23 07:53:54 - INFO - Загрузка настроек пользователей...
2025-07-23 07:53:54 - INFO - Настройки пользователей загружены (67 пользователей)
2025-07-23 07:53:54 - INFO - Настройки пользователей успешно загружены.
2025-07-23 07:53:54 - INFO - Инициализация базы данных...
2025-07-23 07:53:54 - INFO - Database initialized successfully
2025-07-23 07:53:54 - INFO - База данных успешно инициализирована.
2025-07-23 07:53:54 - INFO - Unified background scheduler started
2025-07-23 07:53:54 - INFO - Unified background scheduler thread started (оптимизированный).
2025-07-23 07:53:54 - INFO - Cleanup scheduler disabled by configuration (AUTO_CLEANUP_ENABLED=False)
2025-07-23 07:53:54 - INFO - Bot sh is starting...
2025-07-23 07:53:54 - INFO - MAIN: Bot polling starting...
2025-07-23 07:53:59 - INFO - 🔥 Запуск разогрева соединений для быстрого отклика...
2025-07-23 07:53:59 - INFO - [GenAI] Starting connection warmup for 2 keys
2025-07-23 07:54:02 - INFO - [GenAI] Connection warmup completed: 2/2 successful
2025-07-23 07:54:02 - INFO - ✅ Разогрев завершен: 2 соединений готовы
2025-07-23 07:54:05 - INFO - ThreadPoolExecutor created with 16 workers
2025-07-23 07:54:05 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: павел дуров интервью с путиным на сайте
очень агрессивный диалог, особенно путин пиздец злой на дуро...
2025-07-23 07:55:06 - INFO - HTML Group: Successfully generated and sent HTML file 'павел_дуров_интервью.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:56:13 - INFO - user 5090434457 (@Qwesad1246) (Qwe) - Ignoring reply to HTML file 492060
2025-07-23 07:58:05 - INFO - HTML Group: Request from @kirillshsh in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: рыбалка полноценная 3д игра...
2025-07-23 07:58:54 - INFO - Rate limiter cleanup completed. Active users: 1 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 0 private podcasts, 0 AI responses, 0 summaries
2025-07-23 07:58:54 - INFO - Force cleanup: removed 0 completed futures
2025-07-23 07:58:54 - INFO - Scheduled podcast checker started
2025-07-23 07:59:23 - INFO - HTML Group: Successfully generated and sent HTML file 'рыбалка_полноценная_3д.html' for @kirillshsh using Gemini 2.5 Pro
2025-07-23 07:59:33 - INFO - HTML Group: Request from ID:7994657045 in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Сто восемь.О музыке.
Те ,кто долго жил среди пидарасов,говорят,что они втайне стыдятся своего греха ...
2025-07-23 08:00:22 - INFO - HTML Group: Successfully generated and sent HTML file 'сто_восемь_о.html' for ID:7994657045 using Gemini 2.5 Pro
2025-07-23 08:06:34 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: 'судьба трамвайного движения в городе бе́щастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе́щастево это город-миллионник, цыган в бе́щастево больше людей, трамваи хоть и новые для города, но старые как мир'
2025-07-23 08:06:59 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: '[судьба трамвайного движения в городе бе‌щастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе‌щастево это город-миллионник, цыган в бе‌щастево больше людей, трамваи хоть и новые для города, но старые как мир]'
2025-07-23 08:07:09 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:07:09 - INFO - Generated process key 15d0261b85f8 for user ********** in chat **********
2025-07-23 08:07:09 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Starting direct thematic podcast processing (no queue) - theme: '[судьба трамвайного движения в городе бе‌щастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе‌щастево это город-миллионник, цыган в бе‌щастево больше людей, трамваи хоть и новые для города, но старые как мир]'
2025-07-23 08:07:09 - INFO - Started thematic podcast thread 15d0261b85f8 for user ********** in chat **********
2025-07-23 08:07:09 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:07:09 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Starting thematic podcast generation for theme: '[судьба трамвайного движения в городе бе‌щастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе‌щастево это город-миллионник, цыган в бе‌щастево больше людей, трамваи хоть и новые для города, но старые как мир]'
2025-07-23 08:07:09 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Process key: 15d0261b85f8
2025-07-23 08:07:09 - INFO - [15d0261b85f8] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:07:09 - INFO - [15d0261b85f8] Starting status manager for chat **********, msg 29221
2025-07-23 08:07:09 - INFO - Recorded private podcast request for user **********
2025-07-23 08:07:09 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Starting research stage for thematic podcast
2025-07-23 08:07:09 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Generating research queries for theme: [судьба трамвайного движения в городе бе‌щастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе‌щастево это город-миллионник, цыган в бе‌щастево больше людей, трамваи хоть и новые для города, но старые как мир]
2025-07-23 08:07:15 - INFO - [Research_query_generation/ResearchQueries-GenAI] All API keys failed for research query generation
2025-07-23 08:07:15 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Generated 3 research queries: ['[судьба трамвайного движения в городе бе\u200cщастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе\u200cщастево это город-миллионник, цыган в бе\u200cщастево больше людей, трамваи хоть и новые для города, но старые как мир] обзор', '[судьба трамвайного движения в городе бе\u200cщастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе\u200cщастево это город-миллионник, цыган в бе\u200cщастево больше людей, трамваи хоть и новые для города, но старые как мир] последние новости', '[судьба трамвайного движения в городе бе\u200cщастево, открыто 21 октября 1932 года, теперь осталось только два маршрута с общей конечной "вокзал", 8 маршрут "вокзал - проспект цыган", 3 маршрут "вокзал - дом цыганского барона", обращались к президенту зимбабве, москвабад и президент зимбабве не помог, бе\u200cщастево это город-миллионник, цыган в бе\u200cщастево больше людей, трамваи хоть и новые для города, но старые как мир] экспертное мнение']
2025-07-23 08:07:15 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:07:17 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: [судьба трамвайного движения в городе бе‌щастево, ...
2025-07-23 08:07:17 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: [судьба трамвайного движения в городе бе‌щастево, ...
2025-07-23 08:07:25 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: [судьба трамвайного движения в городе бе‌щастево, ...
2025-07-23 08:07:25 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:07:25 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Research completed, got 3 results
2025-07-23 08:07:25 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Using Diana & Sasha system prompt for thematic podcast
2025-07-23 08:07:25 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Prepared research information for system prompt (2707 characters)
2025-07-23 08:07:25 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Generating thematic podcast dialogue
2025-07-23 08:07:56 - WARNING - [PerformanceMonitor] Slow request detected: 30.17s for unknown with key ...sFME
2025-07-23 08:07:56 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Generated thematic dialogue (4817 characters)
2025-07-23 08:07:56 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Generating thematic podcast audio
2025-07-23 08:07:56 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Diana & Sasha voices: Vindemiatrix, Algenib
2025-07-23 08:07:56 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (4953 characters with prefix)
2025-07-23 08:07:56 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:07:56 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:10:20 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (16574010 bytes)
2025-07-23 08:10:20 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_**********_1753258220.pcm
2025-07-23 08:10:20 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_**********_1753258220.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_**********_1753258220.mp3
2025-07-23 08:10:22 - INFO - TTS audio successfully converted to MP3: /tmp/tts_**********_1753258220.mp3
2025-07-23 08:10:22 - INFO - Stopped status manager for chat **********, msg 29221
2025-07-23 08:10:22 - INFO - [15d0261b85f8] Unregistered podcast process (chat: **********)
2025-07-23 08:10:22 - INFO - Successfully deleted message 29221 in chat **********
2025-07-23 08:10:22 - INFO - [15d0261b85f8] Status message deleted successfully
2025-07-23 08:10:22 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:10:23 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Cleaned up audio file: /tmp/tts_**********_1753258220.mp3
2025-07-23 08:10:23 - INFO - [15d0261b85f8] Unregistered podcast process (chat: **********)
2025-07-23 08:10:23 - INFO - Message 29221 in chat ********** already deleted or not found
2025-07-23 08:10:23 - INFO - [15d0261b85f8] Status message deleted successfully
2025-07-23 08:10:23 - INFO - user ********** (chat **********) [process 15d0261b85f8] - Thematic podcast generation completed successfully
2025-07-23 08:15:54 - INFO - Started animated welcome sequence for user 1305781987.
2025-07-23 08:16:05 - INFO - Completed animated welcome sequence for user 1305781987
2025-07-23 08:18:55 - INFO - Cleaned up 1 old podcast themes
2025-07-23 08:18:55 - INFO - Общая очистка завершена: удалено 1 записей
2025-07-23 08:18:55 - INFO - Scheduled podcast checker started
2025-07-23 08:19:18 - INFO - Started animated welcome sequence for user 5617320213.
2025-07-23 08:19:30 - INFO - Completed animated welcome sequence for user 5617320213
2025-07-23 08:21:22 - INFO - user 5617320213 (chat 5617320213) - Requested podcast generation for 24 hours with theme: 'аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая'
2025-07-23 08:22:35 - INFO - user 5617320213 (chat 5617320213) - Requested podcast generation for 24 hours with theme: 'аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая'
2025-07-23 08:22:42 - INFO - Anonymous callback ALLOWED: user 5617320213 -> podcast_confirm (original: 5617320213, chat: 5617320213, reason: Same user)
2025-07-23 08:22:42 - INFO - Generated process key 97ea648e54da for user 5617320213 in chat 5617320213
2025-07-23 08:22:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Starting direct thematic podcast processing (no queue) - theme: 'аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая'
2025-07-23 08:22:42 - INFO - Started thematic podcast thread 97ea648e54da for user 5617320213 in chat 5617320213
2025-07-23 08:22:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:22:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Starting thematic podcast generation for theme: 'аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая'
2025-07-23 08:22:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Process key: 97ea648e54da
2025-07-23 08:22:42 - INFO - [97ea648e54da] Registered podcast process (type: thematic, chat: 5617320213)
2025-07-23 08:22:42 - INFO - [97ea648e54da] Starting status manager for chat 5617320213, msg 29237
2025-07-23 08:22:42 - INFO - Recorded private podcast request for user 5617320213
2025-07-23 08:22:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Starting research stage for thematic podcast
2025-07-23 08:22:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Generating research queries for theme: аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая
2025-07-23 08:22:47 - INFO - [Research_query_generation/ResearchQueries-GenAI] Generated 0 queries instead of 3. Raw response: AJERNA аналоговые хорроры особенности стиля
AJERNA Смешарики хоррор лор
AJERNA саннсао головного мозга ыстык шоу объяснение
2025-07-23 08:22:47 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Generated 3 research queries: ['аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая обзор', 'аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая последние новости', 'аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая экспертное мнение']
2025-07-23 08:22:47 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:23:02 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: аналоговые хорроры от AJERNA. Смешарики, саннсао г...
2025-07-23 08:23:05 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: аналоговые хорроры от AJERNA. Смешарики, саннсао г...
2025-07-23 08:23:09 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Empty search response for query 3: аналоговые хорроры от AJERNA. Смешарики, саннсао головного мозга, ыстык саннсао шоу. AJERNA самая лучшая экспертное мнение
2025-07-23 08:23:09 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] All API keys failed for search 3
2025-07-23 08:23:09 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 2 successful
2025-07-23 08:23:09 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Research completed, got 3 results
2025-07-23 08:23:10 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:23:10 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Prepared research information for system prompt (3405 characters)
2025-07-23 08:23:10 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Generating thematic podcast dialogue
2025-07-23 08:23:37 - WARNING - [PerformanceMonitor] Slow request detected: 27.25s for unknown with key ...Mm3U
2025-07-23 08:23:37 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Generated thematic dialogue (5934 characters)
2025-07-23 08:23:37 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Generating thematic podcast audio
2025-07-23 08:23:37 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:23:37 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (6098 characters with prefix)
2025-07-23 08:23:37 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:23:37 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:26:39 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (19849530 bytes)
2025-07-23 08:26:39 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_5617320213_1753259199.pcm
2025-07-23 08:26:39 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_5617320213_1753259199.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_5617320213_1753259199.mp3
2025-07-23 08:26:41 - INFO - TTS audio successfully converted to MP3: /tmp/tts_5617320213_1753259199.mp3
2025-07-23 08:26:41 - INFO - Stopped status manager for chat 5617320213, msg 29237
2025-07-23 08:26:41 - INFO - [97ea648e54da] Unregistered podcast process (chat: 5617320213)
2025-07-23 08:26:41 - INFO - Successfully deleted message 29237 in chat 5617320213
2025-07-23 08:26:41 - INFO - [97ea648e54da] Status message deleted successfully
2025-07-23 08:26:41 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:26:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Cleaned up audio file: /tmp/tts_5617320213_1753259199.mp3
2025-07-23 08:26:42 - INFO - [97ea648e54da] Unregistered podcast process (chat: 5617320213)
2025-07-23 08:26:42 - INFO - Message 29237 in chat 5617320213 already deleted or not found
2025-07-23 08:26:42 - INFO - [97ea648e54da] Status message deleted successfully
2025-07-23 08:26:42 - INFO - user 5617320213 (chat 5617320213) [process 97ea648e54da] - Thematic podcast generation completed successfully
2025-07-23 08:29:47 - INFO - Started animated welcome sequence for user **********.
2025-07-23 08:29:59 - INFO - Completed animated welcome sequence for user **********
2025-07-23 08:33:35 - INFO - Started animated welcome sequence for user **********.
2025-07-23 08:33:47 - INFO - Completed animated welcome sequence for user **********
2025-07-23 08:34:03 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 08:34:03 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 08:34:03 - INFO - Notified admins about new Diana & Sasha podcast request from user **********
2025-07-23 08:34:03 - INFO - New user ********** detected in private chat - admins notified for Diana & Sasha approval
2025-07-23 08:34:03 - INFO - user ********** (@mezoik) (mezoik) - Set INSTANT ⚡ reaction (text/Flash default) on message 29251 in private chat
2025-07-23 08:34:03 - INFO - Utils set_reaction (chat **********, msg 29251, emoji ⚡): Successfully set reaction.
2025-07-23 08:34:04 - INFO - user ********** - DIRECT audio/video group: 1 files. Initial bot status_id from handler: None
2025-07-23 08:34:04 - INFO - user ********** - Skipping reaction setting - instant reaction already set for private chat
2025-07-23 08:34:04 - INFO - user ********** - Starting download & conversion for 1 files.
2025-07-23 08:34:04 - INFO - user ********** - Using global thread pool for downloads (global limit: 8 concurrent tasks)
2025-07-23 08:34:04 - INFO - user ********** - Starting download/convert for file index 0 (type: voice)
2025-07-23 08:34:05 - INFO - user ********** - Downloaded file 1 to '/tmp/tmp0_7006hd/02819d2f-9959-47de-8b09-92378a967b98_orig.mp3' (6617591 bytes)
2025-07-23 08:34:05 - INFO - === FFmpeg Operation Started ===
2025-07-23 08:34:05 - INFO - Operation: MP3 conversion
2025-07-23 08:34:05 - INFO - Input files: ['/tmp/tmp0_7006hd/02819d2f-9959-47de-8b09-92378a967b98_orig.mp3']
2025-07-23 08:34:05 - INFO - Output file: /tmp/tmp0_7006hd/b5eef20e-dab0-45d8-9aba-633fe8ef0d9e.mp3
2025-07-23 08:34:05 - INFO - System resources at start: CPU 18.7%, Memory 35.7%, Disk 373.04GB free
2025-07-23 08:34:05 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmp0_7006hd/02819d2f-9959-47de-8b09-92378a967b98_orig.mp3 -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmp0_7006hd/b5eef20e-dab0-45d8-9aba-633fe8ef0d9e.mp3
2025-07-23 08:34:07 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 1.96s
2025-07-23 08:34:07 - INFO - === FFmpeg Operation Ended ===
2025-07-23 08:34:07 - INFO - Operation: MP3 conversion
2025-07-23 08:34:07 - INFO - Status: SUCCESS
2025-07-23 08:34:07 - INFO - Duration: 1.96 seconds
2025-07-23 08:34:07 - INFO - System resources at end: CPU 11.8%, Memory 35.7%
2025-07-23 08:34:07 - INFO - === End FFmpeg Operation Log ===
2025-07-23 08:34:07 - INFO - ffmpeg conversion successful for '/tmp/tmp0_7006hd/02819d2f-9959-47de-8b09-92378a967b98_orig.mp3'. Output: /tmp/tmp0_7006hd/b5eef20e-dab0-45d8-9aba-633fe8ef0d9e.mp3
2025-07-23 08:34:07 - INFO - user ********** - Starting transcription for 1 converted files.
2025-07-23 08:34:07 - INFO - user ********** - Using global thread pool for transcriptions (global limit: 8 concurrent tasks)
2025-07-23 08:34:07 - INFO - user ********** - Starting transcription for file index 0 ('/tmp/tmp0_7006hd/b5eef20e-dab0-45d8-9aba-633fe8ef0d9e.mp3') using Gemini
2025-07-23 08:34:07 - INFO - user ********** - Processing audio file 1 (3921259 bytes)
2025-07-23 08:34:07 - INFO - user ********** - Base64 encoded audio size: 5228348 characters
2025-07-23 08:34:07 - INFO - Admin ********** approved user ********** for Diana & Sasha podcast
2025-07-23 08:34:17 - WARNING - [PerformanceMonitor] Slow request detected: 10.28s for unknown with key ...tNZ0
2025-07-23 08:34:18 - INFO - user ********** - Direct audio group transcribed. Passing to process_text_or_photo_request. Combined length: 5571
2025-07-23 08:34:18 - INFO - Recorded AI response request for user **********
2025-07-23 08:34:18 - INFO - user ********** - Reaction will be set after model determination
2025-07-23 08:34:18 - INFO - user ********** - Private chat detected. Using main prompt with user info and individual context (context_key: **********)
2025-07-23 08:34:18 - INFO - user ********** - Started new typing manager
2025-07-23 08:34:18 - INFO - user ********** - Retrieved conversation history with 0 messages for context_key: **********
2025-07-23 08:34:18 - INFO - user ********** - No model override. Defaulting to primary: gemini-2.5-pro.
2025-07-23 08:34:18 - INFO - user ********** - Query classified as ULTRAPRO, using Gemini 2.5 Pro with enhanced reasoning
2025-07-23 08:34:18 - INFO - user ********** - Scheduled reaction update to 👨‍💻 in 1 second for Pro model
2025-07-23 08:34:18 - INFO - user ********** - Attempting regular API call with gemini-2.5-pro for group chat...
2025-07-23 08:34:18 - INFO - Router: model_name='gemini-2.5-pro', call_type='general_primary', is_private_chat=False
2025-07-23 08:34:18 - INFO - Routing to gemini-2.5-pro for group chat: call_type=general_primary
2025-07-23 08:34:19 - INFO - Utils set_reaction (chat **********, msg 29251, emoji 👨‍💻): Successfully set reaction.
2025-07-23 08:34:19 - INFO - user ********** - Updated reaction to 👨‍💻 for Pro model after 1 second
2025-07-23 08:34:28 - INFO - user ********** - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit).
2025-07-23 08:34:28 - INFO - Utils send_long_message (chat **********): Message not long (375 chars). Sending as single message.
2025-07-23 08:34:28 - INFO - Utils send_long_message (chat **********): Sent single message as new message 29254.
2025-07-23 08:34:28 - INFO - user ********** - send_long_message sent final response. Bot message ID: 29254.
2025-07-23 08:34:28 - INFO - Utils set_reaction (chat **********, msg 29251, emoji 🎉): Successfully set reaction.
2025-07-23 08:34:28 - INFO - user ********** - Added '🎉' reaction to USER message 29251 after successful response.
2025-07-23 08:34:28 - INFO - _prepare_summarize_button_if_needed: button creation disabled (chat_id: **********)
2025-07-23 08:34:28 - INFO - user ********** - final_reply_markup is None.
2025-07-23 08:34:28 - INFO - Настройки пользователей сохранены в /home/<USER>/Бот Подкаст/bot_data.json
2025-07-23 08:34:28 - INFO - user ********** - Cleaned up data for direct audio/video group from global state (parallel revert).
2025-07-23 08:35:09 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:35:09 - INFO - Cleaned up 1 old podcast themes
2025-07-23 08:35:16 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:35:16 - INFO - Generated process key 9d87b3f85ebb for user ********** in chat **********
2025-07-23 08:35:16 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Starting direct thematic podcast processing (no queue) - theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:35:16 - INFO - Started thematic podcast thread 9d87b3f85ebb for user ********** in chat **********
2025-07-23 08:35:16 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:35:16 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Starting thematic podcast generation for theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:35:16 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Process key: 9d87b3f85ebb
2025-07-23 08:35:16 - INFO - [9d87b3f85ebb] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:35:16 - INFO - [9d87b3f85ebb] Starting status manager for chat **********, msg 29257
2025-07-23 08:35:16 - INFO - Recorded private podcast request for user **********
2025-07-23 08:35:16 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Starting research stage for thematic podcast
2025-07-23 08:35:16 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Generating research queries for theme: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,
2025-07-23 08:35:21 - INFO - [Research_query_generation/ResearchQueries-GenAI] Generated 0 queries instead of 3. Raw response: Анализ эфирного времени российских телеканалов
Сериал Интерны невышедшие или удаленные серии
Киноляпы и ошибки в российской телерекламе
2025-07-23 08:35:21 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Generated 3 research queries: ['@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, обзор', '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, последние новости', '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, экспертное мнение']
2025-07-23 08:35:21 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:35:21 - INFO - Stopped status manager for chat **********, msg 29257
2025-07-23 08:35:21 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:35:28 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: 'пиписькииииааааа'
2025-07-23 08:35:29 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 08:35:29 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 08:35:29 - INFO - Notified admins about new Diana & Sasha podcast request from user **********
2025-07-23 08:35:29 - INFO - New user ********** detected in private chat - admins notified for Diana & Sasha approval
2025-07-23 08:35:29 - INFO - user ********** (@jops2286) (Kanye) - Set INSTANT ⚡ reaction (text/Flash default) on message 29263 in private chat
2025-07-23 08:35:29 - INFO - Utils set_reaction (chat **********, msg 29263, emoji ⚡): Successfully set reaction.
2025-07-23 08:35:29 - INFO - user ********** (@jops2286) (Kanye) - Reaction will be set after model determination in processing_core.
2025-07-23 08:35:29 - INFO - user ********** - processing request buffer with 1 items.
2025-07-23 08:35:29 - INFO - user ********** - combined buffer: text length=3, num_images=0, reply_to=29263
2025-07-23 08:35:29 - INFO - Recorded AI response request for user **********
2025-07-23 08:35:29 - INFO - user ********** - Reaction will be set after model determination
2025-07-23 08:35:29 - INFO - user ********** - Private chat detected. Using main prompt with user info and individual context (context_key: **********)
2025-07-23 08:35:29 - INFO - user ********** - Started new typing manager
2025-07-23 08:35:29 - INFO - user ********** - Retrieved conversation history with 0 messages for context_key: **********
2025-07-23 08:35:29 - INFO - user ********** - No model override. Defaulting to primary: gemini-2.5-pro.
2025-07-23 08:35:30 - INFO - user ********** - Query classified as LITE, using Gemini 2.5 Flash Lite
2025-07-23 08:35:30 - INFO - user ********** - Attempting regular API call with gemini-2.5-flash for group chat...
2025-07-23 08:35:30 - INFO - Router: model_name='gemini-2.5-flash', call_type='general_primary', is_private_chat=False
2025-07-23 08:35:30 - INFO - Routing to gemini-2.5-flash for group chat: call_type=general_primary
2025-07-23 08:35:32 - INFO - Admin ********** approved user ********** for Diana & Sasha podcast
2025-07-23 08:35:32 - INFO - Started animated welcome sequence for user **********.
2025-07-23 08:35:35 - INFO - user ********** - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit).
2025-07-23 08:35:35 - INFO - Utils send_long_message (chat **********): Message not long (65 chars). Sending as single message.
2025-07-23 08:35:35 - INFO - Utils send_long_message (chat **********): Sent single message as new message 29271.
2025-07-23 08:35:35 - INFO - user ********** - send_long_message sent final response. Bot message ID: 29271.
2025-07-23 08:35:35 - INFO - Utils set_reaction (chat **********, msg 29263, emoji 🎉): Successfully set reaction.
2025-07-23 08:35:35 - INFO - user ********** - Added '🎉' reaction to USER message 29263 after successful response.
2025-07-23 08:35:35 - INFO - _prepare_summarize_button_if_needed: button creation disabled (chat_id: **********)
2025-07-23 08:35:35 - INFO - user ********** - final_reply_markup is None.
2025-07-23 08:35:37 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телек...
2025-07-23 08:35:37 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:35:38 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:35:38 - INFO - Generated process key e62abe495156 for user ********** in chat **********
2025-07-23 08:35:38 - INFO - user ********** (chat **********) [process e62abe495156] - Starting direct thematic podcast processing (no queue) - theme: 'пиписькииииааааа'
2025-07-23 08:35:38 - INFO - Started thematic podcast thread e62abe495156 for user ********** in chat **********
2025-07-23 08:35:38 - INFO - user ********** (chat **********) [process e62abe495156] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:35:38 - INFO - user ********** (chat **********) [process e62abe495156] - Starting thematic podcast generation for theme: 'пиписькииииааааа'
2025-07-23 08:35:38 - INFO - user ********** (chat **********) [process e62abe495156] - Process key: e62abe495156
2025-07-23 08:35:38 - INFO - [e62abe495156] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:35:38 - INFO - [e62abe495156] Starting status manager for chat **********, msg 29262
2025-07-23 08:35:38 - INFO - Recorded private podcast request for user **********
2025-07-23 08:35:38 - INFO - user ********** (chat **********) [process e62abe495156] - Starting research stage for thematic podcast
2025-07-23 08:35:38 - INFO - user ********** (chat **********) [process e62abe495156] - Generating research queries for theme: пиписькииииааааа
2025-07-23 08:35:43 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телек...
2025-07-23 08:35:43 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:35:44 - INFO - [Research_query_generation/ResearchQueries-GenAI] All API keys failed for research query generation
2025-07-23 08:35:44 - INFO - user ********** (chat **********) [process e62abe495156] - Generated 3 research queries: ['пиписькииииааааа обзор', 'пиписькииииааааа последние новости', 'пиписькииииааааа экспертное мнение']
2025-07-23 08:35:44 - INFO - Completed animated welcome sequence for user **********
2025-07-23 08:35:44 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телек...
2025-07-23 08:35:44 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:35:44 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:35:44 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:35:44 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Research completed, got 3 results
2025-07-23 08:35:44 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:35:44 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Prepared research information for system prompt (11283 characters)
2025-07-23 08:35:44 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:35:44 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Generating thematic podcast dialogue
2025-07-23 08:35:46 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: пиписькииииааааа экспертное мнение...
2025-07-23 08:35:46 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: пиписькииииааааа обзор...
2025-07-23 08:35:53 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: пиписькииииааааа последние новости...
2025-07-23 08:35:53 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:35:53 - INFO - user ********** (chat **********) [process e62abe495156] - Research completed, got 3 results
2025-07-23 08:35:53 - INFO - user ********** (chat **********) [process e62abe495156] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:35:53 - INFO - user ********** (chat **********) [process e62abe495156] - Prepared research information for system prompt (987 characters)
2025-07-23 08:35:53 - INFO - user ********** (chat **********) [process e62abe495156] - Generating thematic podcast dialogue
2025-07-23 08:35:56 - WARNING - [PerformanceMonitor] Slow request detected: 12.10s for unknown with key ...KBQ0
2025-07-23 08:36:05 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: 'SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:36:09 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:36:09 - INFO - Generated process key 0e26cd62e27d for user ********** in chat **********
2025-07-23 08:36:09 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Starting direct thematic podcast processing (no queue) - theme: 'SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:36:09 - INFO - Started thematic podcast thread 0e26cd62e27d for user ********** in chat **********
2025-07-23 08:36:09 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:36:09 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Starting thematic podcast generation for theme: 'SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:36:09 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Process key: 0e26cd62e27d
2025-07-23 08:36:09 - INFO - [0e26cd62e27d] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:36:09 - INFO - [0e26cd62e27d] Starting status manager for chat **********, msg 29273
2025-07-23 08:36:09 - INFO - Recorded private podcast request for user **********
2025-07-23 08:36:09 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Starting research stage for thematic podcast
2025-07-23 08:36:09 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Generating research queries for theme: SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,
2025-07-23 08:36:17 - INFO - [Research_query_generation/ResearchQueries-GenAI] Generated 0 queries instead of 3. Raw response: Сериал Интерны: существуют ли секретные или невыпущенные серии?
Анализ феномена пропадающих предметов в российской телерекламе: причины и примеры
Регулирование и фактическая продолжительность вещания российских федеральных телеканалов
2025-07-23 08:36:17 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Generated 3 research queries: ['SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, обзор', 'SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, последние новости', 'SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, экспертное мнение']
2025-07-23 08:36:17 - WARNING - Status message 29273 not found. Stopping updates.
2025-07-23 08:36:17 - INFO - Stopped status manager for chat **********, msg 29273
2025-07-23 08:36:17 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:36:18 - WARNING - [PerformanceMonitor] Slow request detected: 22.01s for unknown with key ...2jlQ
2025-07-23 08:36:18 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Generated thematic dialogue (4887 characters)
2025-07-23 08:36:18 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:36:18 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Generating thematic podcast audio
2025-07-23 08:36:18 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:36:18 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (5051 characters with prefix)
2025-07-23 08:36:18 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:36:18 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:36:26 - WARNING - [PerformanceMonitor] Slow request detected: 22.56s for unknown with key ...K4Nw
2025-07-23 08:36:26 - INFO - user ********** (chat **********) [process e62abe495156] - Generated thematic dialogue (4970 characters)
2025-07-23 08:36:26 - INFO - user ********** (chat **********) [process e62abe495156] - Generating thematic podcast audio
2025-07-23 08:36:26 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:36:26 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (5134 characters with prefix)
2025-07-23 08:36:26 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:36:26 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:36:36 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телека...
2025-07-23 08:36:36 - WARNING - Status message 29273 not found. Stopping updates.
2025-07-23 08:36:37 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телека...
2025-07-23 08:36:37 - WARNING - Status message 29273 not found. Stopping updates.
2025-07-23 08:36:38 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телека...
2025-07-23 08:36:38 - WARNING - Status message 29273 not found. Stopping updates.
2025-07-23 08:36:38 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:36:38 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Research completed, got 3 results
2025-07-23 08:36:38 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:36:38 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Prepared research information for system prompt (11028 characters)
2025-07-23 08:36:38 - WARNING - Status message 29273 not found. Stopping updates.
2025-07-23 08:36:38 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Generating thematic podcast dialogue
2025-07-23 08:37:02 - WARNING - [PerformanceMonitor] Slow request detected: 23.79s for unknown with key ...NE1U
2025-07-23 08:37:02 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Generated thematic dialogue (4935 characters)
2025-07-23 08:37:02 - WARNING - Status message 29273 not found. Stopping updates.
2025-07-23 08:37:02 - INFO - user ********** (chat **********) [process 0e26cd62e27d] - Generating thematic podcast audio
2025-07-23 08:37:02 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:37:02 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (5099 characters with prefix)
2025-07-23 08:37:02 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:37:02 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:37:05 - INFO - user ********** (@jops2286) (Kanye) - Set INSTANT ⚡ reaction (text/Flash default) on message 29274 in private chat
2025-07-23 08:37:05 - INFO - Utils set_reaction (chat **********, msg 29274, emoji ⚡): Successfully set reaction.
2025-07-23 08:37:05 - INFO - user ********** (@jops2286) (Kanye) - Reaction will be set after model determination in processing_core.
2025-07-23 08:37:05 - INFO - user ********** - processing request buffer with 1 items.
2025-07-23 08:37:05 - INFO - user ********** - combined buffer: text length=3, num_images=0, reply_to=29274
2025-07-23 08:37:05 - INFO - Recorded AI response request for user **********
2025-07-23 08:37:05 - INFO - user ********** - Reaction will be set after model determination
2025-07-23 08:37:05 - INFO - user ********** - Private chat detected. Using main prompt with user info and individual context (context_key: **********)
2025-07-23 08:37:05 - INFO - user ********** - Started new typing manager
2025-07-23 08:37:05 - INFO - user ********** - Retrieved conversation history with 2 messages for context_key: **********
2025-07-23 08:37:05 - INFO - user ********** - No model override. Defaulting to primary: gemini-2.5-pro.
2025-07-23 08:37:06 - INFO - user ********** - Query classified as LITE, using Gemini 2.5 Flash Lite
2025-07-23 08:37:06 - INFO - user ********** - Attempting regular API call with gemini-2.5-flash for group chat...
2025-07-23 08:37:06 - INFO - Router: model_name='gemini-2.5-flash', call_type='general_primary', is_private_chat=False
2025-07-23 08:37:06 - INFO - Routing to gemini-2.5-flash for group chat: call_type=general_primary
2025-07-23 08:37:06 - INFO - user ********** - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit).
2025-07-23 08:37:06 - INFO - Utils send_long_message (chat **********): Message not long (20 chars). Sending as single message.
2025-07-23 08:37:06 - INFO - Utils send_long_message (chat **********): Sent single message as new message 29275.
2025-07-23 08:37:06 - INFO - user ********** - send_long_message sent final response. Bot message ID: 29275.
2025-07-23 08:37:07 - INFO - Utils set_reaction (chat **********, msg 29274, emoji 🎉): Successfully set reaction.
2025-07-23 08:37:07 - INFO - user ********** - Added '🎉' reaction to USER message 29274 after successful response.
2025-07-23 08:37:07 - INFO - _prepare_summarize_button_if_needed: button creation disabled (chat_id: **********)
2025-07-23 08:37:07 - INFO - user ********** - final_reply_markup is None.
2025-07-23 08:37:08 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 08:37:08 - INFO - Sent Diana approval notification to admin ********** for user **********
2025-07-23 08:37:08 - INFO - Notified admins about new Diana & Sasha podcast request from user **********
2025-07-23 08:37:08 - INFO - New user ********** detected in private chat - admins notified for Diana & Sasha approval
2025-07-23 08:37:08 - INFO - user ********** (@a_n_t_a_r_c) (⋆✴︎˚｡⋆ ˖⋆ ˚❆) - Set INSTANT ⚡ reaction (text/Flash default) on message 29276 in private chat
2025-07-23 08:37:09 - INFO - Utils set_reaction (chat **********, msg 29276, emoji ⚡): Successfully set reaction.
2025-07-23 08:37:09 - INFO - user ********** (@a_n_t_a_r_c) (⋆✴︎˚｡⋆ ˖⋆ ˚❆) - Reaction will be set after model determination in processing_core.
2025-07-23 08:37:09 - INFO - user ********** - processing request buffer with 1 items.
2025-07-23 08:37:09 - INFO - user ********** - combined buffer: text length=28, num_images=0, reply_to=29276
2025-07-23 08:37:09 - INFO - Recorded AI response request for user **********
2025-07-23 08:37:09 - INFO - user ********** - Reaction will be set after model determination
2025-07-23 08:37:09 - INFO - user ********** - Private chat detected. Using main prompt with user info and individual context (context_key: **********)
2025-07-23 08:37:09 - INFO - user ********** - Started new typing manager
2025-07-23 08:37:09 - INFO - user ********** - Retrieved conversation history with 0 messages for context_key: **********
2025-07-23 08:37:09 - INFO - user ********** - No model override. Defaulting to primary: gemini-2.5-pro.
2025-07-23 08:37:09 - INFO - user ********** - Query classified as LITE, using Gemini 2.5 Flash Lite
2025-07-23 08:37:09 - INFO - user ********** - Attempting regular API call with gemini-2.5-flash for group chat...
2025-07-23 08:37:09 - INFO - Router: model_name='gemini-2.5-flash', call_type='general_primary', is_private_chat=False
2025-07-23 08:37:09 - INFO - Routing to gemini-2.5-flash for group chat: call_type=general_primary
2025-07-23 08:37:12 - INFO - Admin ********** approved user ********** for Diana & Sasha podcast
2025-07-23 08:37:12 - INFO - user ********** - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit).
2025-07-23 08:37:12 - INFO - Utils send_long_message (chat **********): Message not long (59 chars). Sending as single message.
2025-07-23 08:37:12 - INFO - Utils send_long_message (chat **********): Sent single message as new message 29279.
2025-07-23 08:37:12 - INFO - user ********** - send_long_message sent final response. Bot message ID: 29279.
2025-07-23 08:37:12 - INFO - Utils set_reaction (chat **********, msg 29276, emoji 🎉): Successfully set reaction.
2025-07-23 08:37:12 - INFO - user ********** - Added '🎉' reaction to USER message 29276 after successful response.
2025-07-23 08:37:12 - INFO - _prepare_summarize_button_if_needed: button creation disabled (chat_id: **********)
2025-07-23 08:37:12 - INFO - user ********** - final_reply_markup is None.
2025-07-23 08:37:44 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:37:47 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:37:47 - INFO - Generated process key 6b92a58d4923 for user ********** in chat **********
2025-07-23 08:37:47 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Starting direct thematic podcast processing (no queue) - theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:37:47 - INFO - Started thematic podcast thread 6b92a58d4923 for user ********** in chat **********
2025-07-23 08:37:47 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:37:47 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Starting thematic podcast generation for theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:37:47 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Process key: 6b92a58d4923
2025-07-23 08:37:47 - INFO - [6b92a58d4923] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:37:47 - INFO - [6b92a58d4923] Starting status manager for chat **********, msg 29281
2025-07-23 08:37:47 - INFO - Recorded private podcast request for user **********
2025-07-23 08:37:47 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Starting research stage for thematic podcast
2025-07-23 08:37:47 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Generating research queries for theme: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,
2025-07-23 08:37:52 - INFO - [Research_query_generation/ResearchQueries-GenAI] Generated 0 queries instead of 3. Raw response: Регулирование вещания российских телеканалов
Сериал Интерны невышедшие серии
Киноляпы в телевизионной рекламе
2025-07-23 08:37:52 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Generated 3 research queries: ['@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, обзор', '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, последние новости', '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, экспертное мнение']
2025-07-23 08:37:52 - WARNING - Status message 29281 not found. Stopping updates.
2025-07-23 08:37:52 - INFO - Stopped status manager for chat **********, msg 29281
2025-07-23 08:37:52 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:37:52 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Error during search 2 for query '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв, последние новости': 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key not valid. Please pass a valid API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key not valid. Please pass a valid API key.'}]}}
2025-07-23 08:37:52 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] All API keys failed for search 2
2025-07-23 08:37:55 - INFO - Started animated welcome sequence for user 714704399.
2025-07-23 08:38:06 - INFO - Completed animated welcome sequence for user 714704399
2025-07-23 08:38:08 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телек...
2025-07-23 08:38:08 - WARNING - Status message 29281 not found. Stopping updates.
2025-07-23 08:38:11 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: @SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телек...
2025-07-23 08:38:12 - WARNING - Status message 29281 not found. Stopping updates.
2025-07-23 08:38:12 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 2 successful
2025-07-23 08:38:12 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Research completed, got 3 results
2025-07-23 08:38:12 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Using Diana & Sasha system prompt for thematic podcast
2025-07-23 08:38:12 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Prepared research information for system prompt (8054 characters)
2025-07-23 08:38:12 - WARNING - Status message 29281 not found. Stopping updates.
2025-07-23 08:38:12 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Generating thematic podcast dialogue
2025-07-23 08:38:15 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:38:19 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:38:19 - INFO - Generated process key 95b7e9172416 for user ********** in chat **********
2025-07-23 08:38:19 - INFO - user ********** (chat **********) [process 95b7e9172416] - Starting direct thematic podcast processing (no queue) - theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:38:19 - INFO - Started thematic podcast thread 95b7e9172416 for user ********** in chat **********
2025-07-23 08:38:19 - INFO - user ********** (chat **********) [process 95b7e9172416] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:38:19 - INFO - user ********** (chat **********) [process 95b7e9172416] - Starting thematic podcast generation for theme: '@SCJWFEIEVEIMGODJEGRIWGEGEHS,часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:38:19 - INFO - user ********** (chat **********) [process 95b7e9172416] - Process key: 95b7e9172416
2025-07-23 08:38:19 - INFO - [95b7e9172416] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:38:19 - INFO - [95b7e9172416] Starting status manager for chat **********, msg 29292
2025-07-23 08:38:19 - INFO - Stopped status manager for chat **********, msg 29292
2025-07-23 08:38:19 - ERROR - Error showing error status: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: MESSAGE_ID_INVALID
2025-07-23 08:38:54 - WARNING - [PerformanceMonitor] Slow request detected: 31.50s for unknown with key ...SFiA
2025-07-23 08:38:54 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Generated thematic dialogue (5105 characters)
2025-07-23 08:38:54 - WARNING - Status message 29281 not found. Stopping updates.
2025-07-23 08:38:54 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Generating thematic podcast audio
2025-07-23 08:38:54 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Diana & Sasha voices: Vindemiatrix, Algenib
2025-07-23 08:38:54 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (5255 characters with prefix)
2025-07-23 08:38:54 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:38:54 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:38:55 - INFO - Rate limiter cleanup completed. Active users: 12 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 4 private podcasts, 3 AI responses, 0 summaries
2025-07-23 08:38:55 - INFO - Scheduled podcast checker started
2025-07-23 08:38:56 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: 'часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:38:58 - INFO - Anonymous callback ALLOWED: user ********** -> podcast_confirm (original: **********, chat: **********, reason: Same user)
2025-07-23 08:38:58 - INFO - Generated process key e87e8b3a3f20 for user ********** in chat **********
2025-07-23 08:38:58 - INFO - user ********** (chat **********) [process e87e8b3a3f20] - Starting direct thematic podcast processing (no queue) - theme: 'часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:38:58 - INFO - Started thematic podcast thread e87e8b3a3f20 for user ********** in chat **********
2025-07-23 08:38:58 - INFO - user ********** (chat **********) [process e87e8b3a3f20] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:38:58 - INFO - user ********** (chat **********) [process e87e8b3a3f20] - Starting thematic podcast generation for theme: 'часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:38:58 - INFO - user ********** (chat **********) [process e87e8b3a3f20] - Process key: e87e8b3a3f20
2025-07-23 08:38:58 - INFO - [e87e8b3a3f20] Registered podcast process (type: thematic, chat: **********)
2025-07-23 08:38:58 - INFO - [e87e8b3a3f20] Starting status manager for chat **********, msg 29300
2025-07-23 08:38:58 - INFO - Stopped status manager for chat **********, msg 29300
2025-07-23 08:39:05 - INFO - user 714704399 (chat 714704399) - Requested podcast generation for 24 hours with theme: 'саннсао проект аналоговых хорроров'
2025-07-23 08:39:06 - INFO - user ********** (chat **********) - Requested podcast generation for 24 hours with theme: 'часы российских телеканалов,сериал интерны и секретные серии,пропадающие предметы в рекламах на тв,'
2025-07-23 08:39:12 - INFO - Anonymous callback ALLOWED: user 714704399 -> podcast_confirm (original: 714704399, chat: 714704399, reason: Same user)
2025-07-23 08:39:12 - INFO - Generated process key 67e79e320de6 for user 714704399 in chat 714704399
2025-07-23 08:39:12 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Starting direct thematic podcast processing (no queue) - theme: 'саннсао проект аналоговых хорроров'
2025-07-23 08:39:12 - INFO - Started thematic podcast thread 67e79e320de6 for user 714704399 in chat 714704399
2025-07-23 08:39:12 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:39:12 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Starting thematic podcast generation for theme: 'саннсао проект аналоговых хорроров'
2025-07-23 08:39:12 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Process key: 67e79e320de6
2025-07-23 08:39:12 - INFO - [67e79e320de6] Registered podcast process (type: thematic, chat: 714704399)
2025-07-23 08:39:12 - INFO - [67e79e320de6] Starting status manager for chat 714704399, msg 29302
2025-07-23 08:39:12 - INFO - Recorded private podcast request for user 714704399
2025-07-23 08:39:12 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Starting research stage for thematic podcast
2025-07-23 08:39:12 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Generating research queries for theme: саннсао проект аналоговых хорроров
2025-07-23 08:39:18 - INFO - [Research_query_generation/ResearchQueries-GenAI] Generated 0 queries instead of 3. Raw response: Что такое Саннсао проект аналоговых хорроров
Саннса
2025-07-23 08:39:18 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Generated 3 research queries: ['саннсао проект аналоговых хорроров обзор', 'саннсао проект аналоговых хорроров последние новости', 'саннсао проект аналоговых хорроров экспертное мнение']
2025-07-23 08:39:18 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:39:20 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (16504890 bytes)
2025-07-23 08:39:20 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_**********_1753259960.pcm
2025-07-23 08:39:20 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_**********_1753259960.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_**********_1753259960.mp3
2025-07-23 08:39:22 - INFO - TTS audio successfully converted to MP3: /tmp/tts_**********_1753259960.mp3
2025-07-23 08:39:22 - INFO - Stopped status manager for chat **********, msg 29262
2025-07-23 08:39:22 - INFO - [e62abe495156] Unregistered podcast process (chat: **********)
2025-07-23 08:39:22 - INFO - Successfully deleted message 29262 in chat **********
2025-07-23 08:39:22 - INFO - [e62abe495156] Status message deleted successfully
2025-07-23 08:39:22 - INFO - user ********** (chat **********) [process e62abe495156] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:39:23 - INFO - user ********** (chat **********) [process e62abe495156] - Cleaned up audio file: /tmp/tts_**********_1753259960.mp3
2025-07-23 08:39:23 - INFO - [e62abe495156] Unregistered podcast process (chat: **********)
2025-07-23 08:39:23 - INFO - Message 29262 in chat ********** already deleted or not found
2025-07-23 08:39:23 - INFO - [e62abe495156] Status message deleted successfully
2025-07-23 08:39:23 - INFO - user ********** (chat **********) [process e62abe495156] - Thematic podcast generation completed successfully
2025-07-23 08:39:31 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: саннсао проект аналоговых хорроров обзор...
2025-07-23 08:39:31 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: саннсао проект аналоговых хорроров последние новос...
2025-07-23 08:39:33 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: саннсао проект аналоговых хорроров экспертное мнен...
2025-07-23 08:39:33 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:39:33 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Research completed, got 3 results
2025-07-23 08:39:33 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:39:33 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Prepared research information for system prompt (5750 characters)
2025-07-23 08:39:33 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Generating thematic podcast dialogue
2025-07-23 08:39:35 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (17213370 bytes)
2025-07-23 08:39:35 - WARNING - Status message 29257 not found. Stopping updates.
2025-07-23 08:39:35 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_**********_1753259975.pcm
2025-07-23 08:39:35 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_**********_1753259975.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_**********_1753259975.mp3
2025-07-23 08:39:37 - INFO - TTS audio successfully converted to MP3: /tmp/tts_**********_1753259975.mp3
2025-07-23 08:39:37 - INFO - [9d87b3f85ebb] Unregistered podcast process (chat: **********)
2025-07-23 08:39:37 - INFO - Message 29257 in chat ********** already deleted or not found
2025-07-23 08:39:37 - INFO - [9d87b3f85ebb] Status message deleted successfully
2025-07-23 08:39:37 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:39:38 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Cleaned up audio file: /tmp/tts_**********_1753259975.mp3
2025-07-23 08:39:38 - INFO - [9d87b3f85ebb] Unregistered podcast process (chat: **********)
2025-07-23 08:39:38 - INFO - Message 29257 in chat ********** already deleted or not found
2025-07-23 08:39:38 - INFO - [9d87b3f85ebb] Status message deleted successfully
2025-07-23 08:39:38 - INFO - user ********** (chat **********) [process 9d87b3f85ebb] - Thematic podcast generation completed successfully
2025-07-23 08:39:52 - INFO - Started animated welcome sequence for user 7673883507.
2025-07-23 08:39:57 - WARNING - [PerformanceMonitor] Slow request detected: 24.58s for unknown with key ...ytqw
2025-07-23 08:39:57 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Generated thematic dialogue (5209 characters)
2025-07-23 08:39:57 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Generating thematic podcast audio
2025-07-23 08:39:57 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:39:57 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (5373 characters with prefix)
2025-07-23 08:39:57 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:39:57 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:40:03 - INFO - Completed animated welcome sequence for user 7673883507
2025-07-23 08:41:05 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Error during generation: 500 INTERNAL. {'error': {'code': 500, 'message': 'An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting', 'status': 'INTERNAL'}}
2025-07-23 08:41:05 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:41:05 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:42:09 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (17971770 bytes)
2025-07-23 08:42:09 - WARNING - Status message 29281 not found. Stopping updates.
2025-07-23 08:42:09 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_**********_1753260129.pcm
2025-07-23 08:42:09 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_**********_1753260129.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_**********_1753260129.mp3
2025-07-23 08:42:11 - INFO - TTS audio successfully converted to MP3: /tmp/tts_**********_1753260129.mp3
2025-07-23 08:42:11 - INFO - [6b92a58d4923] Unregistered podcast process (chat: **********)
2025-07-23 08:42:11 - INFO - Message 29281 in chat ********** already deleted or not found
2025-07-23 08:42:11 - INFO - [6b92a58d4923] Status message deleted successfully
2025-07-23 08:42:11 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:42:11 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Cleaned up audio file: /tmp/tts_**********_1753260129.mp3
2025-07-23 08:42:11 - INFO - [6b92a58d4923] Unregistered podcast process (chat: **********)
2025-07-23 08:42:11 - INFO - Message 29281 in chat ********** already deleted or not found
2025-07-23 08:42:11 - INFO - [6b92a58d4923] Status message deleted successfully
2025-07-23 08:42:11 - INFO - user ********** (chat **********) [process 6b92a58d4923] - Thematic podcast generation completed successfully
2025-07-23 08:42:19 - INFO - user 7673883507 (chat 7673883507) - Requested podcast generation for 24 hours with theme: 'очередной выпуск передачи на Эхо Москвы под названием "Час антисемитизма" в максимально агрессивной форме'
2025-07-23 08:42:40 - INFO - Admin ********** blocked user 7673883507
2025-07-23 08:44:03 - INFO - Started animated welcome sequence for user 6948708078.
2025-07-23 08:44:05 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (17639610 bytes)
2025-07-23 08:44:05 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_714704399_1753260245.pcm
2025-07-23 08:44:05 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_714704399_1753260245.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_714704399_1753260245.mp3
2025-07-23 08:44:07 - INFO - TTS audio successfully converted to MP3: /tmp/tts_714704399_1753260245.mp3
2025-07-23 08:44:07 - INFO - Stopped status manager for chat 714704399, msg 29302
2025-07-23 08:44:07 - INFO - [67e79e320de6] Unregistered podcast process (chat: 714704399)
2025-07-23 08:44:14 - INFO - Completed animated welcome sequence for user 6948708078
2025-07-23 08:44:37 - WARNING - Attempt 1 to delete message 29302 in chat 714704399 failed: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30), retrying...
2025-07-23 08:44:38 - INFO - Successfully deleted message 29302 in chat 714704399
2025-07-23 08:44:38 - INFO - [67e79e320de6] Status message deleted successfully
2025-07-23 08:44:38 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:44:38 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Cleaned up audio file: /tmp/tts_714704399_1753260245.mp3
2025-07-23 08:44:38 - INFO - [67e79e320de6] Unregistered podcast process (chat: 714704399)
2025-07-23 08:44:38 - INFO - Message 29302 in chat 714704399 already deleted or not found
2025-07-23 08:44:38 - INFO - [67e79e320de6] Status message deleted successfully
2025-07-23 08:44:38 - INFO - user 714704399 (chat 714704399) [process 67e79e320de6] - Thematic podcast generation completed successfully
2025-07-23 08:45:19 - INFO - Admin ********** blocked user 6948708078
2025-07-23 08:51:48 - INFO - Started animated welcome sequence for user 6677333091.
2025-07-23 08:52:00 - INFO - Completed animated welcome sequence for user 6677333091
2025-07-23 08:53:15 - INFO - user 6677333091 (chat 6677333091) - Requested podcast generation for 24 hours with theme: 'ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом'
2025-07-23 08:53:15 - INFO - Cleaned up 2 old podcast themes
2025-07-23 08:53:23 - INFO - Anonymous callback ALLOWED: user 6677333091 -> podcast_confirm (original: 6677333091, chat: 6677333091, reason: Same user)
2025-07-23 08:53:23 - INFO - Generated process key 46be0f703d91 for user 6677333091 in chat 6677333091
2025-07-23 08:53:23 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Starting direct thematic podcast processing (no queue) - theme: 'ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом'
2025-07-23 08:53:23 - INFO - Started thematic podcast thread 46be0f703d91 for user 6677333091 in chat 6677333091
2025-07-23 08:53:23 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:53:23 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Starting thematic podcast generation for theme: 'ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом'
2025-07-23 08:53:23 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Process key: 46be0f703d91
2025-07-23 08:53:23 - INFO - [46be0f703d91] Registered podcast process (type: thematic, chat: 6677333091)
2025-07-23 08:53:23 - INFO - [46be0f703d91] Starting status manager for chat 6677333091, msg 29342
2025-07-23 08:53:23 - INFO - Recorded private podcast request for user 6677333091
2025-07-23 08:53:23 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Starting research stage for thematic podcast
2025-07-23 08:53:23 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Generating research queries for theme: ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом
2025-07-23 08:53:29 - INFO - [Research_query_generation/ResearchQueries-GenAI] All API keys failed for research query generation
2025-07-23 08:53:29 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Generated 3 research queries: ['ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом обзор', 'ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом последние новости', 'ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ, ток-шоу пусть говорят, рекламы с суициднальным подтекстом экспертное мнение']
2025-07-23 08:53:29 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:53:42 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ,...
2025-07-23 08:53:44 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ,...
2025-07-23 08:53:47 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: ĐįĕĻ𝟮𝟭𝟬𝟭𝟮𝟬, расчленёнка, аналоговый хоррор про ТВ,...
2025-07-23 08:53:48 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:53:48 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Research completed, got 3 results
2025-07-23 08:53:48 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:53:48 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Prepared research information for system prompt (13214 characters)
2025-07-23 08:53:48 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Generating thematic podcast dialogue
2025-07-23 08:54:27 - WARNING - [PerformanceMonitor] Slow request detected: 24.28s for unknown with key ...4vWE
2025-07-23 08:54:27 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Generated thematic dialogue (5521 characters)
2025-07-23 08:54:27 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Generating thematic podcast audio
2025-07-23 08:54:27 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:54:27 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (5685 characters with prefix)
2025-07-23 08:54:27 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:54:27 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:55:42 - INFO - Started animated welcome sequence for user 5524478309.
2025-07-23 08:55:53 - INFO - Completed animated welcome sequence for user 5524478309
2025-07-23 08:56:26 - INFO - user 5524478309 (chat 5524478309) - Requested podcast generation for 24 hours with theme: 'диньгробесе'
2025-07-23 08:56:32 - INFO - Anonymous callback ALLOWED: user 5524478309 -> podcast_confirm (original: 5524478309, chat: 5524478309, reason: Same user)
2025-07-23 08:56:32 - INFO - Generated process key 7d460fcf7f5a for user 5524478309 in chat 5524478309
2025-07-23 08:56:32 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Starting direct thematic podcast processing (no queue) - theme: 'диньгробесе'
2025-07-23 08:56:32 - INFO - Started thematic podcast thread 7d460fcf7f5a for user 5524478309 in chat 5524478309
2025-07-23 08:56:32 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Queued direct thematic podcast thread (queue size: 1)
2025-07-23 08:56:32 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Starting thematic podcast generation for theme: 'диньгробесе'
2025-07-23 08:56:32 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Process key: 7d460fcf7f5a
2025-07-23 08:56:32 - INFO - [7d460fcf7f5a] Registered podcast process (type: thematic, chat: 5524478309)
2025-07-23 08:56:32 - INFO - [7d460fcf7f5a] Starting status manager for chat 5524478309, msg 29355
2025-07-23 08:56:32 - INFO - Recorded private podcast request for user 5524478309
2025-07-23 08:56:32 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Starting research stage for thematic podcast
2025-07-23 08:56:32 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Generating research queries for theme: диньгробесе
2025-07-23 08:56:37 - INFO - [Research_query_generation/ResearchQueries-GenAI] Generated 0 queries instead of 3. Raw response: Финансовая грамотность для предпринимателей
Управление денежными потоками в малом бизнесе
Источники финансирования для развития бизнеса
2025-07-23 08:56:37 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Generated 3 research queries: ['диньгробесе обзор', 'диньгробесе последние новости', 'диньгробесе экспертное мнение']
2025-07-23 08:56:37 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Starting 3 parallel searches
2025-07-23 08:56:45 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 2 completed for query: диньгробесе последние новости...
2025-07-23 08:56:48 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 3 completed for query: диньгробесе экспертное мнение...
2025-07-23 08:56:48 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Search 1 completed for query: диньгробесе обзор...
2025-07-23 08:56:48 - INFO - [Multiple_web_search/MultipleWebSearch-GenAI] Completed 3 searches, 3 successful
2025-07-23 08:56:48 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Research completed, got 3 results
2025-07-23 08:56:48 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Using Anna & Mikhail system prompt for thematic podcast
2025-07-23 08:56:48 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Prepared research information for system prompt (2096 characters)
2025-07-23 08:56:48 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Generating thematic podcast dialogue
2025-07-23 08:57:00 - WARNING - [PerformanceMonitor] Slow request detected: 11.54s for unknown with key ...KBQ0
2025-07-23 08:57:25 - WARNING - [PerformanceMonitor] Slow request detected: 25.02s for unknown with key ...gEKw
2025-07-23 08:57:25 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Generated thematic dialogue (5897 characters)
2025-07-23 08:57:25 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Generating thematic podcast audio
2025-07-23 08:57:25 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Using Anna & Mikhail voices: Sulafat, Sadaltager
2025-07-23 08:57:25 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Generating multi-speaker audio for dialogue (6054 characters with prefix)
2025-07-23 08:57:25 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Initializing Gemini client...
2025-07-23 08:57:25 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Starting content generation...
2025-07-23 08:57:33 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (18278970 bytes)
2025-07-23 08:57:33 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_6677333091_1753261053.pcm
2025-07-23 08:57:33 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_6677333091_1753261053.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_6677333091_1753261053.mp3
2025-07-23 08:57:35 - INFO - TTS audio successfully converted to MP3: /tmp/tts_6677333091_1753261053.mp3
2025-07-23 08:57:35 - INFO - Stopped status manager for chat 6677333091, msg 29342
2025-07-23 08:57:35 - INFO - [46be0f703d91] Unregistered podcast process (chat: 6677333091)
2025-07-23 08:57:35 - INFO - Successfully deleted message 29342 in chat 6677333091
2025-07-23 08:57:35 - INFO - [46be0f703d91] Status message deleted successfully
2025-07-23 08:57:35 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 08:57:36 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Cleaned up audio file: /tmp/tts_6677333091_1753261053.mp3
2025-07-23 08:57:36 - INFO - [46be0f703d91] Unregistered podcast process (chat: 6677333091)
2025-07-23 08:57:36 - INFO - Message 29342 in chat 6677333091 already deleted or not found
2025-07-23 08:57:36 - INFO - [46be0f703d91] Status message deleted successfully
2025-07-23 08:57:36 - INFO - user 6677333091 (chat 6677333091) [process 46be0f703d91] - Thematic podcast generation completed successfully
2025-07-23 08:58:54 - INFO - [ResourceManager] Cleaned up 2 expired clients
2025-07-23 08:58:56 - INFO - [GenAI] Starting connection warmup for 1 keys
2025-07-23 08:58:56 - INFO - [GenAI] Connection warmup completed: 1/1 successful
2025-07-23 08:58:56 - INFO - Scheduled podcast checker started
2025-07-23 09:00:59 - INFO - [Thematic_podcast_tts/GeminiTTS-GenAI] Successfully generated audio (19603770 bytes)
2025-07-23 09:00:59 - INFO - TTS raw PCM audio saved to: /tmp/tts_raw_5524478309_1753261259.pcm
2025-07-23 09:00:59 - INFO - Converting TTS raw PCM to MP3: ffmpeg -y -f s16le -ar 24000 -ac 1 -i /tmp/tts_raw_5524478309_1753261259.pcm -acodec libmp3lame -b:a 128k -ar 44100 -ac 1 /tmp/tts_5524478309_1753261259.mp3
2025-07-23 09:01:01 - INFO - TTS audio successfully converted to MP3: /tmp/tts_5524478309_1753261259.mp3
2025-07-23 09:01:01 - INFO - Stopped status manager for chat 5524478309, msg 29355
2025-07-23 09:01:01 - INFO - [7d460fcf7f5a] Unregistered podcast process (chat: 5524478309)
2025-07-23 09:01:01 - INFO - Successfully deleted message 29355 in chat 5524478309
2025-07-23 09:01:01 - INFO - [7d460fcf7f5a] Status message deleted successfully
2025-07-23 09:01:01 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Error deleting status message: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: message to delete not found
2025-07-23 09:01:02 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Cleaned up audio file: /tmp/tts_5524478309_1753261259.mp3
2025-07-23 09:01:02 - INFO - [7d460fcf7f5a] Unregistered podcast process (chat: 5524478309)
2025-07-23 09:01:02 - INFO - Message 29355 in chat 5524478309 already deleted or not found
2025-07-23 09:01:02 - INFO - [7d460fcf7f5a] Status message deleted successfully
2025-07-23 09:01:02 - INFO - user 5524478309 (chat 5524478309) [process 7d460fcf7f5a] - Thematic podcast generation completed successfully
2025-07-23 09:03:27 - INFO - HTML Group: Request from @sprwlkmn in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: 3d solar system explorer...
2025-07-23 09:03:30 - ERROR - HTML Group: Gemini API error 503: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29358 in private chat
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29359 in private chat
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29358, emoji 🤔): Successfully set reaction.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - FORWARDED voice (msg_id: 29358). Adding to queue.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Added forwarded voice to queue. Size: 1
2025-07-23 09:03:32 - INFO - Forwarded audio processor is not active. Starting worker.
2025-07-23 09:03:32 - INFO - Forwarded audio queue worker started.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29360 in private chat
2025-07-23 09:03:32 - INFO - Forwarded audio worker processing batch of 1 items.
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29359, emoji 🤔): Successfully set reaction.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - FORWARDED voice (msg_id: 29359). Adding to queue.
2025-07-23 09:03:32 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Created temp_dir: /tmp/tmp5kl4t8yn
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Added forwarded voice to queue. Size: 1
2025-07-23 09:03:32 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Attempting to set reaction '⚡' on message 29358
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29361 in private chat
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29360, emoji 🤔): Successfully set reaction.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - FORWARDED voice (msg_id: 29360). Adding to queue.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Added forwarded voice to queue. Size: 2
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29362 in private chat
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29361, emoji 🤔): Successfully set reaction.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - FORWARDED voice (msg_id: 29361). Adding to queue.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Added forwarded voice to queue. Size: 3
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Set INSTANT 🤔 reaction (ultrathink mode) on message 29363 in private chat
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29358, emoji ⚡): Successfully set reaction.
2025-07-23 09:03:32 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Starting download for file_id AwACAgIAAxkBAAJyrmiApc51LsBCQ0AN9CIkjZB-BSIOAAJ1fAACQG8BSMWtDMce-9fRNgQ
2025-07-23 09:03:32 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Downloading file_id AwACAgIAAxkBAAJyrmiApc51LsBCQ0AN9CIkjZB-BSIOAAJ1fAACQG8BSMWtDMce-9fRNgQ to /tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29362, emoji 🤔): Successfully set reaction.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - FORWARDED voice (msg_id: 29362). Adding to queue.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Added forwarded voice to queue. Size: 4
2025-07-23 09:03:32 - INFO - Utils set_reaction (chat **********, msg 29363, emoji 🤔): Successfully set reaction.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - FORWARDED voice (msg_id: 29363). Adding to queue.
2025-07-23 09:03:32 - INFO - user ********** (@kirilldzfw) (Kirill) - Added forwarded voice to queue. Size: 5
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: File downloaded successfully. Size: 272947 bytes.
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Starting conversion for /tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Converting /tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s to /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:33 - INFO - === FFmpeg Operation Started ===
2025-07-23 09:03:33 - INFO - Operation: MP3 conversion
2025-07-23 09:03:33 - INFO - Input files: ['/tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s']
2025-07-23 09:03:33 - INFO - Output file: /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:33 - INFO - System resources at start: CPU 7.3%, Memory 36.1%, Disk 373.03GB free
2025-07-23 09:03:33 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:33 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 0.33s
2025-07-23 09:03:33 - INFO - === FFmpeg Operation Ended ===
2025-07-23 09:03:33 - INFO - Operation: MP3 conversion
2025-07-23 09:03:33 - INFO - Status: SUCCESS
2025-07-23 09:03:33 - INFO - Duration: 0.33 seconds
2025-07-23 09:03:33 - INFO - System resources at end: CPU 7.9%, Memory 36.1%
2025-07-23 09:03:33 - INFO - === End FFmpeg Operation Log ===
2025-07-23 09:03:33 - INFO - ffmpeg conversion successful for '/tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s'. Output: /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Successfully converted to MP3: /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Removed original temp file: /tmp/tmp5kl4t8yn/94ac57e6-31a2-40ac-9ac1-4e4a1ac29d54_orig_s
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Starting transcription for MP3: /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Base64 audio prepared (163180 chars). Calling Gemini 2.5 Flash for direct audio summary...
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Preparing for Gemini 2.5 Flash call for direct audio summary.
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: System prompt for Gemini 2.5 Flash (first 300 chars): 
Ты обрабатываешь аудио/видео от пользователя Kirill. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• HTML-теги: `<b>`, `<i>`, `<u>`, `<s>`, `<tg-spoiler>`, `<a href="">`, `<pre><code>
• Экранируй спецсимволы в коде: `&...
2025-07-23 09:03:33 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function.
2025-07-23 09:03:33 - INFO - Router: model_name='gemini-2.5-flash', call_type='audio_summary_gemini', is_private_chat=False
2025-07-23 09:03:33 - INFO - Routing to gemini-2.5-flash for call_type: audio_summary_gemini
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Gemini 2.5 Flash raw_output (length: 995): '<<SHORT_SUMMARY_1_START>>
😴 Долгий <b>сон</b> после бессонных ночей.
💡 <b>Ощущение</b> прикольности после отдыха.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
😴 <b>Долгий сон:</b>
Собеседник лёг спать в 22:00 и проспал 12 часов. Это объясняется тем, что предыдущие два дня он совсем не спал, что делает такой долгий сон нормальным и необходимым для восстановления.

💡 <b>Ощущение прикольности:</b>
После пробуждения и использования некой "фигни, которая пар пускает" (предположительно, вейпа или подобного устройства), собеседник выразил ощущение "прикольности", что указывает на хорошее самочувствие и позитивное настроение после полноценного отдыха.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Прикольно, а я лёг в 22 часа, короче, спал, получается, 12 часов. Но это, учитывая, что я прошлые два дня вообще не спал, в целом нормально. А сейчас достал эту фигню, которая, ну, я думаю, ты поняла, которая пар пускает. О, прикольно вообще.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Parsing Gemini 2.5 Flash output. Expecting 1 item.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: DEBUG: Raw output to parse: '<<SHORT_SUMMARY_1_START>>
😴 Долгий <b>сон</b> после бессонных ночей.
💡 <b>Ощущение</b> прикольности после отдыха.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
😴 <b>Долгий сон:</b>
Собеседник лёг спать в 22:00 и проспал 12 часов. Это объясняется тем, что предыдущие два дня он совсем не спал, что делает такой долгий сон нормальным и необходимым для восстановления.

💡 <b>Ощущение прикольности:</b>
После пробуждения и использования некой "фигни, которая пар пускает" (предположительно, вейпа или подобного устройства), собеседник выразил ощущение "прикольности", что указывает на хорошее самочувствие и позитивное настроение после полноценного отдыха.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Прикольно, а я лёг в 22 часа, короче, спал, получается, 12 часов. Но это, учитывая, что я прошлые два дня вообще не спал, в целом нормально. А сейчас достал эту фигню, которая, ну, я думаю, ты поняла, которая пар пускает. О, прикольно вообще.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:36 - INFO - Parser: Final parsed list (first item if exists): {'short_summary': '😴 Долгий <b>сон</b> после бессонных ночей.\n💡 <b>Ощущение</b> прикольности после отдыха.', 'detailed_summary': '😴 <b>Долгий сон:</b>\nСобеседник лёг спать в 22:00 и проспал 12 часов. Это объясняется тем, что предыдущие два дня он совсем не спал, что делает такой долгий сон нормальным и необходимым для восстановления.\n\n💡 <b>Ощущение прикольности:</b>\nПосле пробуждения и использования некой "фигни, которая пар пускает" (предположительно, вейпа или подобного устройства), собеседник выразил ощущение "прикольности", что указывает на хорошее самочувствие и позитивное настроение после полноценного отдыха.', 'formatted_transcript': 'Прикольно, а я лёг в 22 часа, короче, спал, получается, 12 часов. Но это, учитывая, что я прошлые два дня вообще не спал, в целом нормально. А сейчас достал эту фигню, которая, ну, я думаю, ты поняла, которая пар пускает. О, прикольно вообще.'}
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Parsed items: [{'short_summary': '😴 Долгий <b>сон</b> после бессонных ночей.\n💡 <b>Ощущение</b> прикольности после отдыха.', 'detailed_summary': '😴 <b>Долгий сон:</b>\nСобеседник лёг спать в 22:00 и проспал 12 часов. Это объясняется тем, что предыдущие два дня он совсем не спал, что делает такой долгий сон нормальным и необходимым для восстановления.\n\n💡 <b>Ощущение прикольности:</b>\nПосле пробуждения и использования некой "фигни, которая пар пускает" (предположительно, вейпа или подобного устройства), собеседник выразил ощущение "прикольности", что указывает на хорошее самочувствие и позитивное настроение после полноценного отдыха.', 'formatted_transcript': 'Прикольно, а я лёг в 22 часа, короче, спал, получается, 12 часов. Но это, учитывая, что я прошлые два дня вообще не спал, в целом нормально. А сейчас достал эту фигню, которая, ну, я думаю, ты поняла, которая пар пускает. О, прикольно вообще.'}]
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Parser did not return critical error. Short summary from parser: '😴 Долгий <b>сон</b> после бессонных ночей.
💡 <b>Ощущение</b> прикольности после отдыха....'
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Using valid short_summary.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Prepared markup (new logic): {"inline_keyboard": [[{"text": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u0435\u0435", "callback_data": "audio_detail_TEMPKEY"}, {"text": "\u0420\u0430\u0441\u0448\u0438\u0444\u0440\u043e\u0432\u043a\u0430", "callback_data": "audio_trans_TEMPKEY"}]]}
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Attempting to SEND new final summary message.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: SUCCESS - Sent new final summary message (HTML). Final msg_id: 29364
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Updating button callback_data with key: **********_29364
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: SUCCESS - Updated reply_markup with final key.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: SUCCESS - Saved state for single forwarded audio summary. Key: **********_29364
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: END processing successfully. '⚡' reaction on 29358 remains.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Attempting to remove '⚡' reaction from user's message 29358 after successful summary.
2025-07-23 09:03:36 - INFO - Utils remove_reaction (chat **********, msg 29358): Successfully sent request to remove reactions.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Successfully removed '⚡' reaction from user's message 29358.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: END processing successfully.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Entering FINALLY block.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Removing MP3 file: /tmp/tmp5kl4t8yn/c531372c-9977-4aee-bf70-64cb3b8af6fe_s.mp3
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: Removing temp_dir: /tmp/tmp5kl4t8yn
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29358] user **********: FINISHED finally block.
2025-07-23 09:03:36 - INFO - Forwarded audio worker finished processing 1 item.
2025-07-23 09:03:36 - INFO - Forwarded audio worker processing batch of 2 items.
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Created temp_dir: /tmp/tmptqpgnvag
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Attempting to set reaction '⚡' on message 29359
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Created temp_dir: /tmp/tmpg36y2aim
2025-07-23 09:03:36 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Attempting to set reaction '⚡' on message 29360
2025-07-23 09:03:37 - INFO - Utils set_reaction (chat **********, msg 29359, emoji ⚡): Successfully set reaction.
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Starting download for file_id AwACAgIAAxkBAAJyr2iApc7RJrNsw_UrLKP1_C5fMnhcAAJ3fAACQG8BSH9jgbd0g0GgNgQ
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Downloading file_id AwACAgIAAxkBAAJyr2iApc7RJrNsw_UrLKP1_C5fMnhcAAJ3fAACQG8BSH9jgbd0g0GgNgQ to /tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s
2025-07-23 09:03:37 - INFO - Utils set_reaction (chat **********, msg 29360, emoji ⚡): Successfully set reaction.
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Starting download for file_id AwACAgIAAxkBAAJysGiApc5K-ZB1MnXi_enohxStbkpKAAJ7fAACQG8BSAcUGj3K-lNZNgQ
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Downloading file_id AwACAgIAAxkBAAJysGiApc5K-ZB1MnXi_enohxStbkpKAAJ7fAACQG8BSAcUGj3K-lNZNgQ to /tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29359] user **********: File downloaded successfully. Size: 417147 bytes.
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Starting conversion for /tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Converting /tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s to /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:37 - INFO - === FFmpeg Operation Started ===
2025-07-23 09:03:37 - INFO - Operation: MP3 conversion
2025-07-23 09:03:37 - INFO - Input files: ['/tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s']
2025-07-23 09:03:37 - INFO - Output file: /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29360] user **********: File downloaded successfully. Size: 642099 bytes.
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Starting conversion for /tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s
2025-07-23 09:03:37 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Converting /tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s to /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:37 - INFO - === FFmpeg Operation Started ===
2025-07-23 09:03:37 - INFO - Operation: MP3 conversion
2025-07-23 09:03:37 - INFO - Input files: ['/tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s']
2025-07-23 09:03:37 - INFO - Output file: /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:37 - INFO - System resources at start: CPU 15.0%, Memory 36.1%, Disk 373.03GB free
2025-07-23 09:03:37 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:37 - INFO - System resources at start: CPU 13.9%, Memory 36.1%, Disk 373.03GB free
2025-07-23 09:03:37 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:38 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 0.36s
2025-07-23 09:03:38 - INFO - === FFmpeg Operation Ended ===
2025-07-23 09:03:38 - INFO - Operation: MP3 conversion
2025-07-23 09:03:38 - INFO - Status: SUCCESS
2025-07-23 09:03:38 - INFO - Duration: 0.36 seconds
2025-07-23 09:03:38 - INFO - System resources at end: CPU 15.6%, Memory 36.1%
2025-07-23 09:03:38 - INFO - === End FFmpeg Operation Log ===
2025-07-23 09:03:38 - INFO - ffmpeg conversion successful for '/tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s'. Output: /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Successfully converted to MP3: /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Removed original temp file: /tmp/tmptqpgnvag/6758b969-93a7-438e-ac23-49c8c435238c_orig_s
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Starting transcription for MP3: /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Base64 audio prepared (237952 chars). Calling Gemini 2.5 Flash for direct audio summary...
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Preparing for Gemini 2.5 Flash call for direct audio summary.
2025-07-23 09:03:38 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 0.52s
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: System prompt for Gemini 2.5 Flash (first 300 chars): 
Ты обрабатываешь аудио/видео от пользователя Kirill. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• HTML-теги: `<b>`, `<i>`, `<u>`, `<s>`, `<tg-spoiler>`, `<a href="">`, `<pre><code>
• Экранируй спецсимволы в коде: `&...
2025-07-23 09:03:38 - INFO - === FFmpeg Operation Ended ===
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function.
2025-07-23 09:03:38 - INFO - Operation: MP3 conversion
2025-07-23 09:03:38 - INFO - Router: model_name='gemini-2.5-flash', call_type='audio_summary_gemini', is_private_chat=False
2025-07-23 09:03:38 - INFO - Status: SUCCESS
2025-07-23 09:03:38 - INFO - Routing to gemini-2.5-flash for call_type: audio_summary_gemini
2025-07-23 09:03:38 - INFO - Duration: 0.52 seconds
2025-07-23 09:03:38 - INFO - System resources at end: CPU 7.3%, Memory 36.1%
2025-07-23 09:03:38 - INFO - === End FFmpeg Operation Log ===
2025-07-23 09:03:38 - INFO - ffmpeg conversion successful for '/tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s'. Output: /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Successfully converted to MP3: /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Removed original temp file: /tmp/tmpg36y2aim/b6c87311-8d4a-4ffa-a771-935ce05f7c36_orig_s
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Starting transcription for MP3: /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Base64 audio prepared (371772 chars). Calling Gemini 2.5 Flash for direct audio summary...
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Preparing for Gemini 2.5 Flash call for direct audio summary.
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: System prompt for Gemini 2.5 Flash (first 300 chars): 
Ты обрабатываешь аудио/видео от пользователя Kirill. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• HTML-теги: `<b>`, `<i>`, `<u>`, `<s>`, `<tg-spoiler>`, `<a href="">`, `<pre><code>
• Экранируй спецсимволы в коде: `&...
2025-07-23 09:03:38 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function.
2025-07-23 09:03:38 - INFO - Router: model_name='gemini-2.5-flash', call_type='audio_summary_gemini', is_private_chat=False
2025-07-23 09:03:38 - INFO - Routing to gemini-2.5-flash for call_type: audio_summary_gemini
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Gemini 2.5 Flash raw_output (length: 1298): '<<SHORT_SUMMARY_1_START>>
🚶‍♀️ Уточнение планов на <b>прогулку</b> сегодня.
😔 <b>Плохое самочувствие</b> вчера и позавчера.
✅ Готовность <b>гулять сегодня</b>.
✍️ Просьба <b>написать</b>, если решит идти.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
🚶‍♀️ <b>Планы на прогулку:</b>
Спрашивается, пойдет ли собеседник гулять сегодня. Автор испытывает скуку и желает провести время на улице.

😔 <b>Проблемы со здоровьем:</b>
Вчера и позавчера автор чувствовал себя плохо, что делало прогулки невозможными, даже если бы его заставляли.

✅ <b>Готовность сегодня:</b>
Сегодня самочувствие нормальное, и автор готов к прогулке, выражая согласие на совместное времяпрепровождение.

✍️ <b>Ожидание ответа:</b>
Несмотря на вчерашние планы не гулять, автор просит написать, если собеседник все же решит пойти на прогулку сегодня.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Блин, ты сегодня точно гулять не пойдёшь? Если пойдёшь, пиши, потому что мне очень скучно.

Я хочу. Вот сегодня у меня уже нормальный день. Вчера я не хотел ни с кем гулять, даже если бы меня заставили, потому что я себя чувствовал фигово. Позавчера тоже, а вот сегодня можно. Сегодня я за.

Ну, ты вчера писал то, что не пойдёшь. Ну, может, пойдёшь? Пиши, короче, если пойдёшь.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Parsing Gemini 2.5 Flash output. Expecting 1 item.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: DEBUG: Raw output to parse: '<<SHORT_SUMMARY_1_START>>
🚶‍♀️ Уточнение планов на <b>прогулку</b> сегодня.
😔 <b>Плохое самочувствие</b> вчера и позавчера.
✅ Готовность <b>гулять сегодня</b>.
✍️ Просьба <b>написать</b>, если решит идти.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
🚶‍♀️ <b>Планы на прогулку:</b>
Спрашивается, пойдет ли собеседник гулять сегодня. Автор испытывает скуку и желает провести время на улице.

😔 <b>Проблемы со здоровьем:</b>
Вчера и позавчера автор чувствовал себя плохо, что делало прогулки невозможными, даже если бы его заставляли.

✅ <b>Готовность сегодня:</b>
Сегодня самочувствие нормальное, и автор готов к прогулке, выражая согласие на совместное времяпрепровождение.

✍️ <b>Ожидание ответа:</b>
Несмотря на вчерашние планы не гулять, автор просит написать, если собеседник все же решит пойти на прогулку сегодня.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Блин, ты сегодня точно гулять не пойдёшь? Если пойдёшь, пиши, потому что мне очень скучно.

Я хочу. Вот сегодня у меня уже нормальный день. Вчера я не хотел ни с кем гулять, даже если бы меня заставили, потому что я себя чувствовал фигово. Позавчера тоже, а вот сегодня можно. Сегодня я за.

Ну, ты вчера писал то, что не пойдёшь. Ну, может, пойдёшь? Пиши, короче, если пойдёшь.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:42 - INFO - Parser: Final parsed list (first item if exists): {'short_summary': '🚶\u200d♀️ Уточнение планов на <b>прогулку</b> сегодня.\n😔 <b>Плохое самочувствие</b> вчера и позавчера.\n✅ Готовность <b>гулять сегодня</b>.\n✍️ Просьба <b>написать</b>, если решит идти.', 'detailed_summary': '🚶\u200d♀️ <b>Планы на прогулку:</b>\nСпрашивается, пойдет ли собеседник гулять сегодня. Автор испытывает скуку и желает провести время на улице.\n\n😔 <b>Проблемы со здоровьем:</b>\nВчера и позавчера автор чувствовал себя плохо, что делало прогулки невозможными, даже если бы его заставляли.\n\n✅ <b>Готовность сегодня:</b>\nСегодня самочувствие нормальное, и автор готов к прогулке, выражая согласие на совместное времяпрепровождение.\n\n✍️ <b>Ожидание ответа:</b>\nНесмотря на вчерашние планы не гулять, автор просит написать, если собеседник все же решит пойти на прогулку сегодня.', 'formatted_transcript': 'Блин, ты сегодня точно гулять не пойдёшь? Если пойдёшь, пиши, потому что мне очень скучно.\n\nЯ хочу. Вот сегодня у меня уже нормальный день. Вчера я не хотел ни с кем гулять, даже если бы меня заставили, потому что я себя чувствовал фигово. Позавчера тоже, а вот сегодня можно. Сегодня я за.\n\nНу, ты вчера писал то, что не пойдёшь. Ну, может, пойдёшь? Пиши, короче, если пойдёшь.'}
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Parsed items: [{'short_summary': '🚶\u200d♀️ Уточнение планов на <b>прогулку</b> сегодня.\n😔 <b>Плохое самочувствие</b> вчера и позавчера.\n✅ Готовность <b>гулять сегодня</b>.\n✍️ Просьба <b>написать</b>, если решит идти.', 'detailed_summary': '🚶\u200d♀️ <b>Планы на прогулку:</b>\nСпрашивается, пойдет ли собеседник гулять сегодня. Автор испытывает скуку и желает провести время на улице.\n\n😔 <b>Проблемы со здоровьем:</b>\nВчера и позавчера автор чувствовал себя плохо, что делало прогулки невозможными, даже если бы его заставляли.\n\n✅ <b>Готовность сегодня:</b>\nСегодня самочувствие нормальное, и автор готов к прогулке, выражая согласие на совместное времяпрепровождение.\n\n✍️ <b>Ожидание ответа:</b>\nНесмотря на вчерашние планы не гулять, автор просит написать, если собеседник все же решит пойти на прогулку сегодня.', 'formatted_transcript': 'Блин, ты сегодня точно гулять не пойдёшь? Если пойдёшь, пиши, потому что мне очень скучно.\n\nЯ хочу. Вот сегодня у меня уже нормальный день. Вчера я не хотел ни с кем гулять, даже если бы меня заставили, потому что я себя чувствовал фигово. Позавчера тоже, а вот сегодня можно. Сегодня я за.\n\nНу, ты вчера писал то, что не пойдёшь. Ну, может, пойдёшь? Пиши, короче, если пойдёшь.'}]
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Parser did not return critical error. Short summary from parser: '🚶‍♀️ Уточнение планов на <b>прогулку</b> сегодня.
😔 <b>Плохое самочувствие</b> вчера и позавчера.
✅ ...'
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Using valid short_summary.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Prepared markup (new logic): {"inline_keyboard": [[{"text": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u0435\u0435", "callback_data": "audio_detail_TEMPKEY"}, {"text": "\u0420\u0430\u0441\u0448\u0438\u0444\u0440\u043e\u0432\u043a\u0430", "callback_data": "audio_trans_TEMPKEY"}]]}
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Attempting to SEND new final summary message.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: SUCCESS - Sent new final summary message (HTML). Final msg_id: 29365
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Updating button callback_data with key: **********_29365
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: SUCCESS - Updated reply_markup with final key.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: SUCCESS - Saved state for single forwarded audio summary. Key: **********_29365
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: END processing successfully. '⚡' reaction on 29359 remains.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Attempting to remove '⚡' reaction from user's message 29359 after successful summary.
2025-07-23 09:03:42 - INFO - Utils remove_reaction (chat **********, msg 29359): Successfully sent request to remove reactions.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Successfully removed '⚡' reaction from user's message 29359.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: END processing successfully.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Entering FINALLY block.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Removing MP3 file: /tmp/tmptqpgnvag/190dacef-84b2-40d6-a77c-43d42322e693_s.mp3
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: Removing temp_dir: /tmp/tmptqpgnvag
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29359] user **********: FINISHED finally block.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Gemini 2.5 Flash raw_output (length: 1508): '<<SHORT_SUMMARY_1_START>>
🤷‍♂️ Автор не <b>знает</b>, где записи.
🚫 <b>Не слушал</b> их с мая или выпускного.
✍️ Последний раз сам <b>включал</b>, когда их писали зимой.
🎬 Включал также при <b>монтаже</b> видеоклипа.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
🤷‍♂️ <b>Местонахождение записей:</b>
Автор признается, что не знает, где находятся эти записи, и давно их не слушал, что указывает на их заброшенность или потерю интереса.

🚫 <b>Последнее прослушивание:</b>
Последний раз записи были прослушаны Викой в мае или на выпускном. Сам автор не слушал их с того момента.

✍️ <b>Процесс создания:</b>
Автор последний раз сам включал записи, когда они еще их писали, что было зимой. Это подчеркивает их давность и связь с процессом создания.

🎬 <b>Монтаж видеоклипа:</b>
Также автор включал записи во время монтажа видеоклипа, что говорит о возможном использовании этих записей в творческом проекте.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Это вообще неправда. Я их, кстати, даже не знаю, где они. Я их уже не слушал, я не помню, ну, с момента, когда Вика включала последний раз. Я не знаю, в мае, наверное. А, нет, на, на этом же, на выпускном они были. Вот я это последний раз их услышал. Но сам я их включал последний раз, когда мы их ещё писали, я не знаю, зимой. Вот, мы последний раз же тогда писали. Ну да. Вот зимой последний раз сам включал.

Come on! Ну или, и когда это монтаж делал, видеоклип. Вот тогда я тоже последний раз включал.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Parsing Gemini 2.5 Flash output. Expecting 1 item.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: DEBUG: Raw output to parse: '<<SHORT_SUMMARY_1_START>>
🤷‍♂️ Автор не <b>знает</b>, где записи.
🚫 <b>Не слушал</b> их с мая или выпускного.
✍️ Последний раз сам <b>включал</b>, когда их писали зимой.
🎬 Включал также при <b>монтаже</b> видеоклипа.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
🤷‍♂️ <b>Местонахождение записей:</b>
Автор признается, что не знает, где находятся эти записи, и давно их не слушал, что указывает на их заброшенность или потерю интереса.

🚫 <b>Последнее прослушивание:</b>
Последний раз записи были прослушаны Викой в мае или на выпускном. Сам автор не слушал их с того момента.

✍️ <b>Процесс создания:</b>
Автор последний раз сам включал записи, когда они еще их писали, что было зимой. Это подчеркивает их давность и связь с процессом создания.

🎬 <b>Монтаж видеоклипа:</b>
Также автор включал записи во время монтажа видеоклипа, что говорит о возможном использовании этих записей в творческом проекте.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Это вообще неправда. Я их, кстати, даже не знаю, где они. Я их уже не слушал, я не помню, ну, с момента, когда Вика включала последний раз. Я не знаю, в мае, наверное. А, нет, на, на этом же, на выпускном они были. Вот я это последний раз их услышал. Но сам я их включал последний раз, когда мы их ещё писали, я не знаю, зимой. Вот, мы последний раз же тогда писали. Ну да. Вот зимой последний раз сам включал.

Come on! Ну или, и когда это монтаж делал, видеоклип. Вот тогда я тоже последний раз включал.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:42 - INFO - Parser: Final parsed list (first item if exists): {'short_summary': '🤷\u200d♂️ Автор не <b>знает</b>, где записи.\n🚫 <b>Не слушал</b> их с мая или выпускного.\n✍️ Последний раз сам <b>включал</b>, когда их писали зимой.\n🎬 Включал также при <b>монтаже</b> видеоклипа.', 'detailed_summary': '🤷\u200d♂️ <b>Местонахождение записей:</b>\nАвтор признается, что не знает, где находятся эти записи, и давно их не слушал, что указывает на их заброшенность или потерю интереса.\n\n🚫 <b>Последнее прослушивание:</b>\nПоследний раз записи были прослушаны Викой в мае или на выпускном. Сам автор не слушал их с того момента.\n\n✍️ <b>Процесс создания:</b>\nАвтор последний раз сам включал записи, когда они еще их писали, что было зимой. Это подчеркивает их давность и связь с процессом создания.\n\n🎬 <b>Монтаж видеоклипа:</b>\nТакже автор включал записи во время монтажа видеоклипа, что говорит о возможном использовании этих записей в творческом проекте.', 'formatted_transcript': 'Это вообще неправда. Я их, кстати, даже не знаю, где они. Я их уже не слушал, я не помню, ну, с момента, когда Вика включала последний раз. Я не знаю, в мае, наверное. А, нет, на, на этом же, на выпускном они были. Вот я это последний раз их услышал. Но сам я их включал последний раз, когда мы их ещё писали, я не знаю, зимой. Вот, мы последний раз же тогда писали. Ну да. Вот зимой последний раз сам включал.\n\nCome on! Ну или, и когда это монтаж делал, видеоклип. Вот тогда я тоже последний раз включал.'}
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Parsed items: [{'short_summary': '🤷\u200d♂️ Автор не <b>знает</b>, где записи.\n🚫 <b>Не слушал</b> их с мая или выпускного.\n✍️ Последний раз сам <b>включал</b>, когда их писали зимой.\n🎬 Включал также при <b>монтаже</b> видеоклипа.', 'detailed_summary': '🤷\u200d♂️ <b>Местонахождение записей:</b>\nАвтор признается, что не знает, где находятся эти записи, и давно их не слушал, что указывает на их заброшенность или потерю интереса.\n\n🚫 <b>Последнее прослушивание:</b>\nПоследний раз записи были прослушаны Викой в мае или на выпускном. Сам автор не слушал их с того момента.\n\n✍️ <b>Процесс создания:</b>\nАвтор последний раз сам включал записи, когда они еще их писали, что было зимой. Это подчеркивает их давность и связь с процессом создания.\n\n🎬 <b>Монтаж видеоклипа:</b>\nТакже автор включал записи во время монтажа видеоклипа, что говорит о возможном использовании этих записей в творческом проекте.', 'formatted_transcript': 'Это вообще неправда. Я их, кстати, даже не знаю, где они. Я их уже не слушал, я не помню, ну, с момента, когда Вика включала последний раз. Я не знаю, в мае, наверное. А, нет, на, на этом же, на выпускном они были. Вот я это последний раз их услышал. Но сам я их включал последний раз, когда мы их ещё писали, я не знаю, зимой. Вот, мы последний раз же тогда писали. Ну да. Вот зимой последний раз сам включал.\n\nCome on! Ну или, и когда это монтаж делал, видеоклип. Вот тогда я тоже последний раз включал.'}]
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Parser did not return critical error. Short summary from parser: '🤷‍♂️ Автор не <b>знает</b>, где записи.
🚫 <b>Не слушал</b> их с мая или выпускного.
✍️ Последний раз...'
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Using valid short_summary.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Prepared markup (new logic): {"inline_keyboard": [[{"text": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u0435\u0435", "callback_data": "audio_detail_TEMPKEY"}, {"text": "\u0420\u0430\u0441\u0448\u0438\u0444\u0440\u043e\u0432\u043a\u0430", "callback_data": "audio_trans_TEMPKEY"}]]}
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Attempting to SEND new final summary message.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: SUCCESS - Sent new final summary message (HTML). Final msg_id: 29366
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Updating button callback_data with key: **********_29366
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: SUCCESS - Updated reply_markup with final key.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: SUCCESS - Saved state for single forwarded audio summary. Key: **********_29366
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: END processing successfully. '⚡' reaction on 29360 remains.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Attempting to remove '⚡' reaction from user's message 29360 after successful summary.
2025-07-23 09:03:42 - INFO - Utils remove_reaction (chat **********, msg 29360): Successfully sent request to remove reactions.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Successfully removed '⚡' reaction from user's message 29360.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: END processing successfully.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Entering FINALLY block.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Removing MP3 file: /tmp/tmpg36y2aim/b19e1235-c54e-49b0-beb5-4a36e11e2caf_s.mp3
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: Removing temp_dir: /tmp/tmpg36y2aim
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29360] user **********: FINISHED finally block.
2025-07-23 09:03:42 - INFO - Forwarded audio worker finished processing batch of 2 items using global thread pool.
2025-07-23 09:03:42 - INFO - Forwarded audio worker processing batch of 2 items.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Created temp_dir: /tmp/tmptkjtiga6
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Attempting to set reaction '⚡' on message 29362
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Created temp_dir: /tmp/tmprn8f9onf
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Attempting to set reaction '⚡' on message 29361
2025-07-23 09:03:42 - INFO - Utils set_reaction (chat **********, msg 29362, emoji ⚡): Successfully set reaction.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Starting download for file_id AwACAgIAAxkBAAJysmiApc5FzTeHSBbRA_0xUPFI2YojAAJ_fAACQG8BSNDmgNh5yzR2NgQ
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Downloading file_id AwACAgIAAxkBAAJysmiApc5FzTeHSBbRA_0xUPFI2YojAAJ_fAACQG8BSNDmgNh5yzR2NgQ to /tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s
2025-07-23 09:03:42 - INFO - Utils set_reaction (chat **********, msg 29361, emoji ⚡): Successfully set reaction.
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Starting download for file_id AwACAgIAAxkBAAJysWiApc6jUMc2ITwGQIqWKLjfec8qAAJ9fAACQG8BSIqj_N6gqwHyNgQ
2025-07-23 09:03:42 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Downloading file_id AwACAgIAAxkBAAJysWiApc6jUMc2ITwGQIqWKLjfec8qAAJ9fAACQG8BSIqj_N6gqwHyNgQ to /tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: File downloaded successfully. Size: 154291 bytes.
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Starting conversion for /tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Converting /tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s to /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:43 - INFO - === FFmpeg Operation Started ===
2025-07-23 09:03:43 - INFO - Operation: MP3 conversion
2025-07-23 09:03:43 - INFO - Input files: ['/tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s']
2025-07-23 09:03:43 - INFO - Output file: /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29361] user **********: File downloaded successfully. Size: 261411 bytes.
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Starting conversion for /tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Converting /tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s to /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:43 - INFO - === FFmpeg Operation Started ===
2025-07-23 09:03:43 - INFO - Operation: MP3 conversion
2025-07-23 09:03:43 - INFO - Input files: ['/tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s']
2025-07-23 09:03:43 - INFO - Output file: /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:43 - INFO - System resources at start: CPU 17.3%, Memory 36.1%, Disk 373.03GB free
2025-07-23 09:03:43 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:43 - INFO - System resources at start: CPU 12.3%, Memory 36.1%, Disk 373.03GB free
2025-07-23 09:03:43 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:43 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 0.29s
2025-07-23 09:03:43 - INFO - === FFmpeg Operation Ended ===
2025-07-23 09:03:43 - INFO - Operation: MP3 conversion
2025-07-23 09:03:43 - INFO - Status: SUCCESS
2025-07-23 09:03:43 - INFO - Duration: 0.29 seconds
2025-07-23 09:03:43 - INFO - System resources at end: CPU 10.7%, Memory 36.1%
2025-07-23 09:03:43 - INFO - === End FFmpeg Operation Log ===
2025-07-23 09:03:43 - INFO - ffmpeg conversion successful for '/tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s'. Output: /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Successfully converted to MP3: /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Removed original temp file: /tmp/tmptkjtiga6/509b3cd6-db51-49a8-a0c7-06897591ebe3_orig_s
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Starting transcription for MP3: /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Base64 audio prepared (96656 chars). Calling Gemini 2.5 Flash for direct audio summary...
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Preparing for Gemini 2.5 Flash call for direct audio summary.
2025-07-23 09:03:43 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 0.45s
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: System prompt for Gemini 2.5 Flash (first 300 chars): 
Ты обрабатываешь аудио/видео от пользователя Kirill. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• HTML-теги: `<b>`, `<i>`, `<u>`, `<s>`, `<tg-spoiler>`, `<a href="">`, `<pre><code>
• Экранируй спецсимволы в коде: `&...
2025-07-23 09:03:43 - INFO - === FFmpeg Operation Ended ===
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function.
2025-07-23 09:03:43 - INFO - Operation: MP3 conversion
2025-07-23 09:03:43 - INFO - Router: model_name='gemini-2.5-flash', call_type='audio_summary_gemini', is_private_chat=False
2025-07-23 09:03:43 - INFO - Status: SUCCESS
2025-07-23 09:03:43 - INFO - Routing to gemini-2.5-flash for call_type: audio_summary_gemini
2025-07-23 09:03:43 - INFO - Duration: 0.45 seconds
2025-07-23 09:03:43 - INFO - System resources at end: CPU 10.4%, Memory 36.1%
2025-07-23 09:03:43 - INFO - === End FFmpeg Operation Log ===
2025-07-23 09:03:43 - INFO - ffmpeg conversion successful for '/tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s'. Output: /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Successfully converted to MP3: /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Removed original temp file: /tmp/tmprn8f9onf/04faf4cb-b1c4-42b6-8dff-c2a97895d3d6_orig_s
2025-07-23 09:03:43 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Starting transcription for MP3: /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:44 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Base64 audio prepared (148304 chars). Calling Gemini 2.5 Flash for direct audio summary...
2025-07-23 09:03:44 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Preparing for Gemini 2.5 Flash call for direct audio summary.
2025-07-23 09:03:44 - INFO - [FWD_AUDIO_ITEM 29361] user **********: System prompt for Gemini 2.5 Flash (first 300 chars): 
Ты обрабатываешь аудио/видео от пользователя Kirill. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• HTML-теги: `<b>`, `<i>`, `<u>`, `<s>`, `<tg-spoiler>`, `<a href="">`, `<pre><code>
• Экранируй спецсимволы в коде: `&...
2025-07-23 09:03:44 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function.
2025-07-23 09:03:44 - INFO - Router: model_name='gemini-2.5-flash', call_type='audio_summary_gemini', is_private_chat=False
2025-07-23 09:03:44 - INFO - Routing to gemini-2.5-flash for call_type: audio_summary_gemini
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Gemini 2.5 Flash raw_output (length: 1087): '<<SHORT_SUMMARY_1_START>>
⚠️ Пользователь <b>выражает беспокойство</b> по поводу включения камеры.
🚫 Упоминает наличие <b>"каверов"</b> в чате, что является проблемой.
🚨 Считает это <b>"кошмаром"</b> и просит не включать камеру.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
⚠️ <b>Опасения из-за камеры:</b>
Пользователь выражает сильное беспокойство по поводу того, была ли включена камера во время записи. Он надеется, что она не была активирована.

🚫 <b>Проблема с "каверами":</b>
Причиной беспокойства является наличие "каверов" в чате, что, по его словам, делает ситуацию крайне нежелательной. Это может быть связано с личными данными или конфиденциальной информацией.

🚨 <b>Эмоциональная реакция:</b>
Пользователь называет ситуацию "кошмаром" и настоятельно просит не включать камеру, подчеркивая серьезность своего опасения.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Я надеюсь, ты не камеру включал, потому что у меня тут в чате ещё каверы есть. Это же вообще кошмар какой-то просто. Не дай бог ты камеру, каверы включала.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Parsing Gemini 2.5 Flash output. Expecting 1 item.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: DEBUG: Raw output to parse: '<<SHORT_SUMMARY_1_START>>
⚠️ Пользователь <b>выражает беспокойство</b> по поводу включения камеры.
🚫 Упоминает наличие <b>"каверов"</b> в чате, что является проблемой.
🚨 Считает это <b>"кошмаром"</b> и просит не включать камеру.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
⚠️ <b>Опасения из-за камеры:</b>
Пользователь выражает сильное беспокойство по поводу того, была ли включена камера во время записи. Он надеется, что она не была активирована.

🚫 <b>Проблема с "каверами":</b>
Причиной беспокойства является наличие "каверов" в чате, что, по его словам, делает ситуацию крайне нежелательной. Это может быть связано с личными данными или конфиденциальной информацией.

🚨 <b>Эмоциональная реакция:</b>
Пользователь называет ситуацию "кошмаром" и настоятельно просит не включать камеру, подчеркивая серьезность своего опасения.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Я надеюсь, ты не камеру включал, потому что у меня тут в чате ещё каверы есть. Это же вообще кошмар какой-то просто. Не дай бог ты камеру, каверы включала.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:46 - INFO - Parser: Final parsed list (first item if exists): {'short_summary': '⚠️ Пользователь <b>выражает беспокойство</b> по поводу включения камеры.\n🚫 Упоминает наличие <b>"каверов"</b> в чате, что является проблемой.\n🚨 Считает это <b>"кошмаром"</b> и просит не включать камеру.', 'detailed_summary': '⚠️ <b>Опасения из-за камеры:</b>\nПользователь выражает сильное беспокойство по поводу того, была ли включена камера во время записи. Он надеется, что она не была активирована.\n\n🚫 <b>Проблема с "каверами":</b>\nПричиной беспокойства является наличие "каверов" в чате, что, по его словам, делает ситуацию крайне нежелательной. Это может быть связано с личными данными или конфиденциальной информацией.\n\n🚨 <b>Эмоциональная реакция:</b>\nПользователь называет ситуацию "кошмаром" и настоятельно просит не включать камеру, подчеркивая серьезность своего опасения.', 'formatted_transcript': 'Я надеюсь, ты не камеру включал, потому что у меня тут в чате ещё каверы есть. Это же вообще кошмар какой-то просто. Не дай бог ты камеру, каверы включала.'}
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Parsed items: [{'short_summary': '⚠️ Пользователь <b>выражает беспокойство</b> по поводу включения камеры.\n🚫 Упоминает наличие <b>"каверов"</b> в чате, что является проблемой.\n🚨 Считает это <b>"кошмаром"</b> и просит не включать камеру.', 'detailed_summary': '⚠️ <b>Опасения из-за камеры:</b>\nПользователь выражает сильное беспокойство по поводу того, была ли включена камера во время записи. Он надеется, что она не была активирована.\n\n🚫 <b>Проблема с "каверами":</b>\nПричиной беспокойства является наличие "каверов" в чате, что, по его словам, делает ситуацию крайне нежелательной. Это может быть связано с личными данными или конфиденциальной информацией.\n\n🚨 <b>Эмоциональная реакция:</b>\nПользователь называет ситуацию "кошмаром" и настоятельно просит не включать камеру, подчеркивая серьезность своего опасения.', 'formatted_transcript': 'Я надеюсь, ты не камеру включал, потому что у меня тут в чате ещё каверы есть. Это же вообще кошмар какой-то просто. Не дай бог ты камеру, каверы включала.'}]
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Parser did not return critical error. Short summary from parser: '⚠️ Пользователь <b>выражает беспокойство</b> по поводу включения камеры.
🚫 Упоминает наличие <b>"кав...'
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Using valid short_summary.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Prepared markup (new logic): {"inline_keyboard": [[{"text": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u0435\u0435", "callback_data": "audio_detail_TEMPKEY"}, {"text": "\u0420\u0430\u0441\u0448\u0438\u0444\u0440\u043e\u0432\u043a\u0430", "callback_data": "audio_trans_TEMPKEY"}]]}
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Attempting to SEND new final summary message.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: SUCCESS - Sent new final summary message (HTML). Final msg_id: 29367
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Updating button callback_data with key: **********_29367
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: SUCCESS - Updated reply_markup with final key.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: SUCCESS - Saved state for single forwarded audio summary. Key: **********_29367
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: END processing successfully. '⚡' reaction on 29362 remains.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Attempting to remove '⚡' reaction from user's message 29362 after successful summary.
2025-07-23 09:03:46 - INFO - Utils remove_reaction (chat **********, msg 29362): Successfully sent request to remove reactions.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Successfully removed '⚡' reaction from user's message 29362.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: END processing successfully.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Entering FINALLY block.
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Removing MP3 file: /tmp/tmptkjtiga6/fa92fcb8-d369-4351-bf48-66f22d5b4d76_s.mp3
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: Removing temp_dir: /tmp/tmptkjtiga6
2025-07-23 09:03:46 - INFO - [FWD_AUDIO_ITEM 29362] user **********: FINISHED finally block.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Gemini 2.5 Flash raw_output (length: 1045): '<<SHORT_SUMMARY_1_START>>
😂 <b>Благодарность</b> за напоминание.
🎶 Планируется <b>переслушать</b> аудиозапись.
🤔 <b>Забыты</b> детали, кроме текста.
😅 Испытывается <b>стыд</b> от прослушанного.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
😂 <b>Благодарность и план:</b>
Собеседник выражает благодарность за напоминание о чём-то, что ему предстоит переслушать. Он сразу же собирается это сделать.

🎶 <b>Забытые детали:</b>
Человек признается, что уже забыл практически всё содержание из того, что ему предстоит прослушать, хотя сам текст он, конечно, помнит.

😅 <b>Испытываемый стыд:</b>
Он отмечает, что ему уже очень стыдно за содержание записи, которую он собирается переслушать. Это указывает на неловкость или смущение по поводу прошлых действий или высказываний.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Спасибо за напоминание. Сейчас пойду переслушаю этот позор. Я уже забыл почти всё оттуда. Не, ну текста, конечно, помню, это не забыть никак. Но придётся, мне стыдно уже очень.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Parsing Gemini 2.5 Flash output. Expecting 1 item.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: DEBUG: Raw output to parse: '<<SHORT_SUMMARY_1_START>>
😂 <b>Благодарность</b> за напоминание.
🎶 Планируется <b>переслушать</b> аудиозапись.
🤔 <b>Забыты</b> детали, кроме текста.
😅 Испытывается <b>стыд</b> от прослушанного.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
😂 <b>Благодарность и план:</b>
Собеседник выражает благодарность за напоминание о чём-то, что ему предстоит переслушать. Он сразу же собирается это сделать.

🎶 <b>Забытые детали:</b>
Человек признается, что уже забыл практически всё содержание из того, что ему предстоит прослушать, хотя сам текст он, конечно, помнит.

😅 <b>Испытываемый стыд:</b>
Он отмечает, что ему уже очень стыдно за содержание записи, которую он собирается переслушать. Это указывает на неловкость или смущение по поводу прошлых действий или высказываний.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Спасибо за напоминание. Сейчас пойду переслушаю этот позор. Я уже забыл почти всё оттуда. Не, ну текста, конечно, помню, это не забыть никак. Но придётся, мне стыдно уже очень.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:47 - INFO - Parser: Final parsed list (first item if exists): {'short_summary': '😂 <b>Благодарность</b> за напоминание.\n🎶 Планируется <b>переслушать</b> аудиозапись.\n🤔 <b>Забыты</b> детали, кроме текста.\n😅 Испытывается <b>стыд</b> от прослушанного.', 'detailed_summary': '😂 <b>Благодарность и план:</b>\nСобеседник выражает благодарность за напоминание о чём-то, что ему предстоит переслушать. Он сразу же собирается это сделать.\n\n🎶 <b>Забытые детали:</b>\nЧеловек признается, что уже забыл практически всё содержание из того, что ему предстоит прослушать, хотя сам текст он, конечно, помнит.\n\n😅 <b>Испытываемый стыд:</b>\nОн отмечает, что ему уже очень стыдно за содержание записи, которую он собирается переслушать. Это указывает на неловкость или смущение по поводу прошлых действий или высказываний.', 'formatted_transcript': 'Спасибо за напоминание. Сейчас пойду переслушаю этот позор. Я уже забыл почти всё оттуда. Не, ну текста, конечно, помню, это не забыть никак. Но придётся, мне стыдно уже очень.'}
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Parsed items: [{'short_summary': '😂 <b>Благодарность</b> за напоминание.\n🎶 Планируется <b>переслушать</b> аудиозапись.\n🤔 <b>Забыты</b> детали, кроме текста.\n😅 Испытывается <b>стыд</b> от прослушанного.', 'detailed_summary': '😂 <b>Благодарность и план:</b>\nСобеседник выражает благодарность за напоминание о чём-то, что ему предстоит переслушать. Он сразу же собирается это сделать.\n\n🎶 <b>Забытые детали:</b>\nЧеловек признается, что уже забыл практически всё содержание из того, что ему предстоит прослушать, хотя сам текст он, конечно, помнит.\n\n😅 <b>Испытываемый стыд:</b>\nОн отмечает, что ему уже очень стыдно за содержание записи, которую он собирается переслушать. Это указывает на неловкость или смущение по поводу прошлых действий или высказываний.', 'formatted_transcript': 'Спасибо за напоминание. Сейчас пойду переслушаю этот позор. Я уже забыл почти всё оттуда. Не, ну текста, конечно, помню, это не забыть никак. Но придётся, мне стыдно уже очень.'}]
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Parser did not return critical error. Short summary from parser: '😂 <b>Благодарность</b> за напоминание.
🎶 Планируется <b>переслушать</b> аудиозапись.
🤔 <b>Забыты</b>...'
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Using valid short_summary.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Prepared markup (new logic): {"inline_keyboard": [[{"text": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u0435\u0435", "callback_data": "audio_detail_TEMPKEY"}, {"text": "\u0420\u0430\u0441\u0448\u0438\u0444\u0440\u043e\u0432\u043a\u0430", "callback_data": "audio_trans_TEMPKEY"}]]}
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Attempting to SEND new final summary message.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: SUCCESS - Sent new final summary message (HTML). Final msg_id: 29368
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Updating button callback_data with key: **********_29368
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: SUCCESS - Updated reply_markup with final key.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: SUCCESS - Saved state for single forwarded audio summary. Key: **********_29368
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: END processing successfully. '⚡' reaction on 29361 remains.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Attempting to remove '⚡' reaction from user's message 29361 after successful summary.
2025-07-23 09:03:47 - INFO - Utils remove_reaction (chat **********, msg 29361): Successfully sent request to remove reactions.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Successfully removed '⚡' reaction from user's message 29361.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: END processing successfully.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Entering FINALLY block.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Removing MP3 file: /tmp/tmprn8f9onf/b5a97db6-447b-421e-b4cf-ab8828b1704f_s.mp3
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: Removing temp_dir: /tmp/tmprn8f9onf
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29361] user **********: FINISHED finally block.
2025-07-23 09:03:47 - INFO - Forwarded audio worker finished processing batch of 2 items using global thread pool.
2025-07-23 09:03:47 - INFO - Forwarded audio worker processing batch of 1 items.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Created temp_dir: /tmp/tmp5xdvls1r
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Attempting to set reaction '⚡' on message 29363
2025-07-23 09:03:47 - INFO - Utils set_reaction (chat **********, msg 29363, emoji ⚡): Successfully set reaction.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Starting download for file_id AwACAgIAAxkBAAJys2iApc5_aJzyTOgIA9VdXyzXv-XYAAJRaQACPvcJSIfSmqs1bhmjNgQ
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Downloading file_id AwACAgIAAxkBAAJys2iApc5_aJzyTOgIA9VdXyzXv-XYAAJRaQACPvcJSIfSmqs1bhmjNgQ to /tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: File downloaded successfully. Size: 655283 bytes.
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Starting conversion for /tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s
2025-07-23 09:03:47 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Converting /tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s to /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:47 - INFO - === FFmpeg Operation Started ===
2025-07-23 09:03:47 - INFO - Operation: MP3 conversion
2025-07-23 09:03:47 - INFO - Input files: ['/tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s']
2025-07-23 09:03:47 - INFO - Output file: /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:47 - INFO - System resources at start: CPU 15.2%, Memory 36.1%, Disk 373.03GB free
2025-07-23 09:03:47 - INFO - Running MP3 conversion with timeout 300s: ffmpeg -i /tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s -vn -ar 44100 -ac 1 -codec:a libmp3lame -b:a 128k -q:a 5 -af aresample=44100 -y /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:48 - INFO - FFmpeg SUCCESS: MP3 conversion completed in 0.44s
2025-07-23 09:03:48 - INFO - === FFmpeg Operation Ended ===
2025-07-23 09:03:48 - INFO - Operation: MP3 conversion
2025-07-23 09:03:48 - INFO - Status: SUCCESS
2025-07-23 09:03:48 - INFO - Duration: 0.44 seconds
2025-07-23 09:03:48 - INFO - System resources at end: CPU 19.7%, Memory 36.1%
2025-07-23 09:03:48 - INFO - === End FFmpeg Operation Log ===
2025-07-23 09:03:48 - INFO - ffmpeg conversion successful for '/tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s'. Output: /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Successfully converted to MP3: /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Removed original temp file: /tmp/tmp5xdvls1r/cbf98611-5533-44bd-ba69-b31e4bec8967_orig_s
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Starting transcription for MP3: /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Base64 audio prepared (401368 chars). Calling Gemini 2.5 Flash for direct audio summary...
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Preparing for Gemini 2.5 Flash call for direct audio summary.
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: System prompt for Gemini 2.5 Flash (first 300 chars): 
Ты обрабатываешь аудио/видео от пользователя Kirill. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• HTML-теги: `<b>`, `<i>`, `<u>`, `<s>`, `<tg-spoiler>`, `<a href="">`, `<pre><code>
• Экранируй спецсимволы в коде: `&...
2025-07-23 09:03:48 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function.
2025-07-23 09:03:48 - INFO - Router: model_name='gemini-2.5-flash', call_type='audio_summary_gemini', is_private_chat=False
2025-07-23 09:03:48 - INFO - Routing to gemini-2.5-flash for call_type: audio_summary_gemini
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Gemini 2.5 Flash raw_output (length: 1495): '<<SHORT_SUMMARY_1_START>>
😂 <b>Позор</b> просто кошмар.
📈 Надо <b>подписчиков</b> в канал.
🔓 Канал будет <b>открыт</b> скоро.
🚫 Больше не выкладываю <b>экстра личного</b>.
🤝 Можно <b>приглашать</b> в канал.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
😂 <b>Позор:</b>
Выражается сильное чувство стыда или неловкости по поводу какой-то ситуации, описываемой как "кошмар".

📈 <b>Привлечение подписчиков:</b>
Автор активно ищет способы увеличить количество подписчиков в своем канале, что является приоритетной задачей.

🔓 <b>Открытие канала:</b>
Планируется сделать канал публичным в ближайшее время, так как автор больше не публикует там очень личную информацию.

🚫 <b>Изменение контента:</b>
Автор подтверждает, что перестал выкладывать в канал "экстра личное" содержимое, что соответствует решению об открытии канала.

🤝 <b>Приглашения:</b>
Разрешается приглашать других людей в канал, если они хотят присоединиться, что способствует его расширению.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Короче, позор просто кошмар. Кстати, ты можешь добавить в канал, если хочешь, точнее, если она хочет. Вот, мне надо подписчиков в канал. Я его, наверное, потом открою скоро, потому что мне уже, я уже там ничего не выкладываю такого экстра личного, хотя вроде раньше тоже не выкладывал. Короче, его надо будет открывать скоро. Вот, можно приглашать.

Вот. Не знаю зачем, правда. Ну, вот, если надо зачем-то, я не знаю, можно заходить. Разрешаю.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Parsing Gemini 2.5 Flash output. Expecting 1 item.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: DEBUG: Raw output to parse: '<<SHORT_SUMMARY_1_START>>
😂 <b>Позор</b> просто кошмар.
📈 Надо <b>подписчиков</b> в канал.
🔓 Канал будет <b>открыт</b> скоро.
🚫 Больше не выкладываю <b>экстра личного</b>.
🤝 Можно <b>приглашать</b> в канал.
<<SHORT_SUMMARY_1_END>>

<<DETAILED_SUMMARY_1_START>>
😂 <b>Позор:</b>
Выражается сильное чувство стыда или неловкости по поводу какой-то ситуации, описываемой как "кошмар".

📈 <b>Привлечение подписчиков:</b>
Автор активно ищет способы увеличить количество подписчиков в своем канале, что является приоритетной задачей.

🔓 <b>Открытие канала:</b>
Планируется сделать канал публичным в ближайшее время, так как автор больше не публикует там очень личную информацию.

🚫 <b>Изменение контента:</b>
Автор подтверждает, что перестал выкладывать в канал "экстра личное" содержимое, что соответствует решению об открытии канала.

🤝 <b>Приглашения:</b>
Разрешается приглашать других людей в канал, если они хотят присоединиться, что способствует его расширению.
<<DETAILED_SUMMARY_1_END>>

<<FORMATTED_TRANSCRIPT_1_START>>
Короче, позор просто кошмар. Кстати, ты можешь добавить в канал, если хочешь, точнее, если она хочет. Вот, мне надо подписчиков в канал. Я его, наверное, потом открою скоро, потому что мне уже, я уже там ничего не выкладываю такого экстра личного, хотя вроде раньше тоже не выкладывал. Короче, его надо будет открывать скоро. Вот, можно приглашать.

Вот. Не знаю зачем, правда. Ну, вот, если надо зачем-то, я не знаю, можно заходить. Разрешаю.
<<FORMATTED_TRANSCRIPT_1_END>>'
2025-07-23 09:03:52 - INFO - Parser: Final parsed list (first item if exists): {'short_summary': '😂 <b>Позор</b> просто кошмар.\n📈 Надо <b>подписчиков</b> в канал.\n🔓 Канал будет <b>открыт</b> скоро.\n🚫 Больше не выкладываю <b>экстра личного</b>.\n🤝 Можно <b>приглашать</b> в канал.', 'detailed_summary': '😂 <b>Позор:</b>\nВыражается сильное чувство стыда или неловкости по поводу какой-то ситуации, описываемой как "кошмар".\n\n📈 <b>Привлечение подписчиков:</b>\nАвтор активно ищет способы увеличить количество подписчиков в своем канале, что является приоритетной задачей.\n\n🔓 <b>Открытие канала:</b>\nПланируется сделать канал публичным в ближайшее время, так как автор больше не публикует там очень личную информацию.\n\n🚫 <b>Изменение контента:</b>\nАвтор подтверждает, что перестал выкладывать в канал "экстра личное" содержимое, что соответствует решению об открытии канала.\n\n🤝 <b>Приглашения:</b>\nРазрешается приглашать других людей в канал, если они хотят присоединиться, что способствует его расширению.', 'formatted_transcript': 'Короче, позор просто кошмар. Кстати, ты можешь добавить в канал, если хочешь, точнее, если она хочет. Вот, мне надо подписчиков в канал. Я его, наверное, потом открою скоро, потому что мне уже, я уже там ничего не выкладываю такого экстра личного, хотя вроде раньше тоже не выкладывал. Короче, его надо будет открывать скоро. Вот, можно приглашать.\n\nВот. Не знаю зачем, правда. Ну, вот, если надо зачем-то, я не знаю, можно заходить. Разрешаю.'}
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Parsed items: [{'short_summary': '😂 <b>Позор</b> просто кошмар.\n📈 Надо <b>подписчиков</b> в канал.\n🔓 Канал будет <b>открыт</b> скоро.\n🚫 Больше не выкладываю <b>экстра личного</b>.\n🤝 Можно <b>приглашать</b> в канал.', 'detailed_summary': '😂 <b>Позор:</b>\nВыражается сильное чувство стыда или неловкости по поводу какой-то ситуации, описываемой как "кошмар".\n\n📈 <b>Привлечение подписчиков:</b>\nАвтор активно ищет способы увеличить количество подписчиков в своем канале, что является приоритетной задачей.\n\n🔓 <b>Открытие канала:</b>\nПланируется сделать канал публичным в ближайшее время, так как автор больше не публикует там очень личную информацию.\n\n🚫 <b>Изменение контента:</b>\nАвтор подтверждает, что перестал выкладывать в канал "экстра личное" содержимое, что соответствует решению об открытии канала.\n\n🤝 <b>Приглашения:</b>\nРазрешается приглашать других людей в канал, если они хотят присоединиться, что способствует его расширению.', 'formatted_transcript': 'Короче, позор просто кошмар. Кстати, ты можешь добавить в канал, если хочешь, точнее, если она хочет. Вот, мне надо подписчиков в канал. Я его, наверное, потом открою скоро, потому что мне уже, я уже там ничего не выкладываю такого экстра личного, хотя вроде раньше тоже не выкладывал. Короче, его надо будет открывать скоро. Вот, можно приглашать.\n\nВот. Не знаю зачем, правда. Ну, вот, если надо зачем-то, я не знаю, можно заходить. Разрешаю.'}]
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Parser did not return critical error. Short summary from parser: '😂 <b>Позор</b> просто кошмар.
📈 Надо <b>подписчиков</b> в канал.
🔓 Канал будет <b>открыт</b> скоро.
...'
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Using valid short_summary.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Prepared markup (new logic): {"inline_keyboard": [[{"text": "\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u0435\u0435", "callback_data": "audio_detail_TEMPKEY"}, {"text": "\u0420\u0430\u0441\u0448\u0438\u0444\u0440\u043e\u0432\u043a\u0430", "callback_data": "audio_trans_TEMPKEY"}]]}
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Attempting to SEND new final summary message.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: SUCCESS - Sent new final summary message (HTML). Final msg_id: 29369
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Updating button callback_data with key: **********_29369
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: SUCCESS - Updated reply_markup with final key.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: SUCCESS - Saved state for single forwarded audio summary. Key: **********_29369
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: END processing successfully. '⚡' reaction on 29363 remains.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Attempting to remove '⚡' reaction from user's message 29363 after successful summary.
2025-07-23 09:03:52 - INFO - Utils remove_reaction (chat **********, msg 29363): Successfully sent request to remove reactions.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Successfully removed '⚡' reaction from user's message 29363.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: END processing successfully.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Entering FINALLY block.
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Removing MP3 file: /tmp/tmp5xdvls1r/6e3fe0d5-fdc7-48a6-9c65-d2ed887d12c9_s.mp3
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: Removing temp_dir: /tmp/tmp5xdvls1r
2025-07-23 09:03:52 - INFO - [FWD_AUDIO_ITEM 29363] user **********: FINISHED finally block.
2025-07-23 09:03:52 - INFO - Forwarded audio worker finished processing 1 item.
2025-07-23 09:03:52 - INFO - Forwarded audio queue is empty. Worker pausing.
2025-07-23 09:03:52 - INFO - Forwarded audio queue worker finished a cycle or exited.
2025-07-23 09:04:36 - INFO - Started animated welcome sequence for user 751052182.
2025-07-23 09:04:47 - INFO - Completed animated welcome sequence for user 751052182
2025-07-23 09:05:18 - INFO - Sent Diana approval notification to admin ********** for user 751052182
2025-07-23 09:05:18 - INFO - Sent Diana approval notification to admin ********** for user 751052182
2025-07-23 09:05:18 - INFO - Notified admins about new Diana & Sasha podcast request from user 751052182
2025-07-23 09:05:18 - INFO - New user 751052182 detected in private chat - admins notified for Diana & Sasha approval
2025-07-23 09:05:18 - INFO - user 751052182 (@egorkino08) (Егор) - Set INSTANT ⚡ reaction (text/Flash default) on message 29379 in private chat
2025-07-23 09:05:18 - INFO - Utils set_reaction (chat 751052182, msg 29379, emoji ⚡): Successfully set reaction.
2025-07-23 09:05:18 - INFO - user 751052182 (@egorkino08) (Егор) - using visible text context for reply.
2025-07-23 09:05:18 - INFO - user 751052182 (@egorkino08) (Егор) - Processing reply to bot message 29378 with context.
2025-07-23 09:05:22 - INFO - Admin ********** approved user 751052182 for Diana & Sasha podcast
2025-07-23 09:08:54 - INFO - [ResourceManager] Cleaned up 5 expired clients
2025-07-23 09:18:57 - INFO - Rate limiter cleanup completed. Active users: 24 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 7 private podcasts, 3 AI responses, 0 summaries
2025-07-23 09:18:57 - INFO - Force cleanup: removed 0 completed futures
2025-07-23 09:18:57 - INFO - Scheduled podcast checker started
2025-07-23 09:23:54 - INFO - [ResourceManager] Cleaned up 4 expired clients
2025-07-23 09:26:06 - INFO - HTML Group: Request from @se7entomo in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Windows Codename: Bermuda Triangle Milestone Build 1984...
2025-07-23 09:27:13 - INFO - HTML Group: Successfully generated and sent HTML file 'windows_codename_bermuda.html' for @se7entomo using Gemini 2.5 Pro
2025-07-23 09:30:52 - INFO - HTML Group: Request from @sprwlkmn in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: 3d solar system exploring; ultra-realistic...
2025-07-23 09:32:05 - INFO - HTML Group: Successfully generated and sent HTML file '3d_solar_system.html' for @sprwlkmn using Gemini 2.5 Pro
2025-07-23 09:32:40 - INFO - HTML Group: Request from @sprwlkmn in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Windows Codename Oslo Build 2627 (Norway spirit; Windows XP themed; with sounds)...
2025-07-23 09:34:16 - INFO - HTML Group: Successfully generated and sent HTML file 'windows_codename_oslo.html' for @sprwlkmn using Gemini 2.5 Pro
2025-07-23 09:35:44 - INFO - HTML Group: Request from @sprwlkmn in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: Windows Vista 2.bat

Рабочий стол Windows Vista. Есть 3 элемента рабочего стола: Корзина, Мой компью...
2025-07-23 09:36:14 - ERROR - HTML Group: Error processing request: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-23 09:37:35 - INFO - HTML Group: Request from @sprwlkmn in group 'чатик 🟫🟦🟪🟥' (ID:-1001508403386) using Gemini 2.5 Pro: страшный файл Windows Vista 2.bat с скримером и установкой Windows Vista 2...
2025-07-23 09:38:05 - ERROR - HTML Group: Error processing request: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-23 09:38:54 - INFO - [ResourceManager] Cleaned up 28 expired clients
2025-07-23 09:38:58 - INFO - Scheduled podcast checker started
2025-07-23 09:43:10 - INFO - user ********** (@kirillshsh) (kirill) - Set INSTANT ⚡ reaction (text/Flash default) on message 29383 in private chat
2025-07-23 09:43:10 - INFO - Utils set_reaction (chat **********, msg 29383, emoji ⚡): Successfully set reaction.
2025-07-23 09:43:10 - INFO - user ********** (@kirillshsh) (kirill) - Reaction will be set after model determination in processing_core.
2025-07-23 09:43:10 - INFO - user ********** - processing request buffer with 1 items.
2025-07-23 09:43:10 - INFO - user ********** - combined buffer: text length=1564, num_images=0, reply_to=29383
2025-07-23 09:43:10 - INFO - user ********** - Reaction will be set after model determination
2025-07-23 09:43:10 - INFO - user ********** - Private chat detected. Using main prompt with user info and individual context (context_key: **********)
2025-07-23 09:43:10 - INFO - user ********** - Started new typing manager
2025-07-23 09:43:10 - INFO - user ********** - Retrieved conversation history with 0 messages for context_key: **********
2025-07-23 09:43:10 - INFO - user ********** - No model override. Defaulting to primary: gemini-2.5-pro.
2025-07-23 09:43:10 - INFO - user ********** - Query classified as LITE, using Gemini 2.5 Flash Lite
2025-07-23 09:43:10 - INFO - user ********** - Attempting regular API call with gemini-2.5-flash for group chat...
2025-07-23 09:43:10 - INFO - Router: model_name='gemini-2.5-flash', call_type='general_primary', is_private_chat=False
2025-07-23 09:43:10 - INFO - Routing to gemini-2.5-flash for group chat: call_type=general_primary
2025-07-23 09:43:12 - INFO - user ********** - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit).
2025-07-23 09:43:12 - INFO - Utils send_long_message (chat **********): Message not long (826 chars). Sending as single message.
2025-07-23 09:43:12 - INFO - Utils send_long_message (chat **********): Sent single message as new message 29384.
2025-07-23 09:43:12 - INFO - user ********** - send_long_message sent final response. Bot message ID: 29384.
2025-07-23 09:43:13 - INFO - Utils set_reaction (chat **********, msg 29383, emoji 🎉): Successfully set reaction.
2025-07-23 09:43:13 - INFO - user ********** - Added '🎉' reaction to USER message 29383 after successful response.
2025-07-23 09:43:13 - INFO - _prepare_summarize_button_if_needed: button creation disabled (chat_id: **********)
2025-07-23 09:43:13 - INFO - user ********** - final_reply_markup is None.
2025-07-23 09:43:55 - INFO - [ResourceManager] Cleaned up 3 expired clients
2025-07-23 09:53:55 - INFO - [ResourceManager] Cleaned up 3 expired clients
2025-07-23 09:58:55 - INFO - [ResourceManager] Cleaned up 3 expired clients
2025-07-23 09:58:58 - INFO - Rate limiter cleanup completed. Active users: 17 messages, 0 podcasts, 0 theme podcasts, 0 video generations, 7 private podcasts, 3 AI responses, 0 summaries
2025-07-23 09:58:58 - INFO - [GenAI] Starting connection warmup for 1 keys
2025-07-23 09:58:59 - INFO - [GenAI] Connection warmup completed: 1/1 successful
2025-07-23 09:58:59 - INFO - [GenAI] All key states reset to normal
2025-07-23 09:58:59 - INFO - Scheduled podcast checker started
2025-07-23 10:03:55 - INFO - [ResourceManager] Cleaned up 4 expired clients
